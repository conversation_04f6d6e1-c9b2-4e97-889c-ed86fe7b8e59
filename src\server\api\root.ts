import { createCallerFactory, createTRPCRouter } from "~/server/api/trpc";
import { newsletterRouter } from "~/server/api/routers/newsletter";
import { signupRouter } from "./routers/signup";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  newsletter: newsletterRouter,
  signup: signupRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);

