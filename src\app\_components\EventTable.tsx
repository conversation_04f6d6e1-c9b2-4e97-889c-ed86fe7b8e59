'use client'
import { useEffect, useRef, useState } from "react";
import EventTableRow from "./EventTableRow";


export default function EventTable() {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLTableElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry?.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 1,
        rootMargin: '-25% 0px -25% 0px' // This creates a smaller trigger area in the middle 50% of the viewport
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);
  return (
    <>
      <table ref={ref} className="w-fit min-w-full table-auto [border-spacing:0_0.2rem] border-separate">
        <tbody>
          <EventTableRow index={11} rounds="3x6" isMainEvent={true} isVisible={isVisible}>MAIN Event</EventTableRow>
          <EventTableRow index={10} rounds="3x6" isMainEvent={true} isVisible={isVisible}>CO-MAIN Event</EventTableRow>
        </tbody>
      </table>
      <table className="w-fit min-w-full table-auto [border-spacing:0_0.2rem] border-separate">
        <tbody>
        {Array.from({ length: 7 }).map((_, i) => (
          <EventTableRow index={9-i} key={i} rounds="6x3" isVisible={isVisible}>Pro Bout</EventTableRow>
        ))}
        {Array.from({ length: 2 }).map((_, i) => (
          <EventTableRow index={9-7-i} key={i} rounds="3x3" isVisible={isVisible}>Semi-Pro Bout</EventTableRow>
        ))}
        </tbody>
      </table>
    </>
  );
}
