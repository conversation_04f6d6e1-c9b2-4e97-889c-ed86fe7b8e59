import { z } from "zod";

export const signupSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  bio: z.string(),
  instagram: z.string().optional(),
  tiktok: z.string().optional(),
  gymName: z.string().min(1, "Gym name is required").optional(),
  coachName: z.string().min(1, "Coach name is required"),
  coachPhoneNumber: z.string().min(1, "Coach phone number is required"),
  coachEmail: z.string().email("Invalid coach email address"),
  region: z.string().min(1, "Region is required"),
  weightClass: z.string().min(1, "Weight class is required"),
  fightRecords: z.string().min(1, "Fight records are required"),
  joinReason: z.string().min(1, "Join reason is required"),
  mediaFiles: z.array(z.string()).optional(),
});

export type SignupFormData = z.infer<typeof signupSchema>;


