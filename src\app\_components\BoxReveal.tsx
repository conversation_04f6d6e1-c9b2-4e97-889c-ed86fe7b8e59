'use client'
import React from "react";
import { useVisibility } from "../providers/VisibilityProvider";

interface BoxRevealProps {
    animation: 'clip-right' | 'clip-left' | 'clip-top' | 'clip-bottom' | 'fade-up' | 'fade-down' | 'fade-left' | 'fade-right' | 'pop';
    children: React.ReactNode
    staggerIndex?: number;
}

export default function BoxReveal({ animation, children, staggerIndex = 0 }: BoxRevealProps) {

  const { isVisible, staggerDelay } = useVisibility();
  const getAnimationStyle = (): React.CSSProperties => {
    const baseStyle = {
      transition: 'all 0.8s cubic-bezier(0.16, 1, 0.3, 1)',
      transitionDelay: staggerDelay ? `${staggerDelay*staggerIndex}ms` : undefined,
    };

    // Animation styles based on type
    switch (animation) {
      case 'clip-right':
        return {
          ...baseStyle,
          clipPath: isVisible ? 'inset(0 0 0 0)' : 'inset(0 100% 0 0)',
        };
      case 'clip-left':
        return {
          ...baseStyle,
          clipPath: isVisible ? 'inset(0 0 0 0)' : 'inset(0 0 0 100%)',
        };
      case 'clip-top':
        return {
          ...baseStyle,
          clipPath: isVisible ? 'inset(0 0 0 0)' : 'inset(100% 0 0 0)',
        };
      case 'clip-bottom':
        return {
          ...baseStyle,
          clipPath: isVisible ? 'inset(0 0 0 0)' : 'inset(0 0 100% 0)',
        };
      case 'fade-up':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(30px)',
        };
      case 'fade-down':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(-30px)',
        };
      case 'fade-left':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(-30px)',
        };
      case 'fade-right':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateX(0)' : 'translateX(30px)',
        };
      case 'pop':
        return {
          ...baseStyle,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'scale(1)' : 'scale(0)',
        };
      default:
        return baseStyle;
    }
  };

  // Ensure children is a valid React element
  if (!React.isValidElement(children)) {
    return <>{children}</>;
  }

  // Define a type for elements with style props
  interface StyledElement {
    props: {
      style?: React.CSSProperties;
      [key: string]: unknown;
    };
  }

  // Cast the child to our interface
  const childElement = children as React.ReactElement & StyledElement;
  const childStyle = childElement.props.style ?? {};

  // Create props object with the correct type
  const newProps = {
    style: { ...getAnimationStyle(), ...childStyle }
  };

  return React.cloneElement(childElement, newProps);
}
