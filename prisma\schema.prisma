// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
    provider = "postgresql"
    // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
    // Further reading:
    // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
    // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
    url      = env("DATABASE_URL")
}

model NewsletterSignup {
    id        String   @id @default(cuid())
    email     String   @unique
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([email])
}

model signup {
  id              String   @id @default(cuid())
  firstName       String
  lastName        String
  email          String
  phoneNumber    String
  dateOfBirth    DateTime
  bio            String?  @db.Text
  instagram      String?
  tiktok         String?
  gymName        String
  coachName      String
  coachPhoneNumber String
  coachEmail     String
  region         String
  weightClass    String
  fightRecords   String   @db.Text
  joinReason     String   @db.Text
  mediaFiles     String[] // Array of file URLs
  status         String   @default("PENDING")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}
