'use client'

import { useEffect, useState } from "react";
import Logo from "../debut/_components/Logo";


export default function SiteIntro() {
    const [showF, setShowF] = useState(true);
    const [show, setShow] = useState(true);
    const [showBox, setShowBox] = useState(false);
    useEffect(() => {
        const timer = setTimeout(() => {
        setShowBox(true);
        }, 200);
        const timer2 = setTimeout(() => {
        setShowF(false);
        }, 1000);
        const timer4 = setTimeout(() => {
        setShowBox(false);
        }, 2300);
        const timer3 = setTimeout(() => {
        setShow(false);
        }, 2500);
        return () => {clearTimeout(timer);clearTimeout(timer2);clearTimeout(timer3);clearTimeout(timer4);}
    }, []);

    return (
        <div className={`fixed pointer-events-none h-full w-full z-50 backdrop-blur-3xl top-0 left-0 flex justify-center items-center transition-all duration-1000 ${show? `opacity-100` : `opacity-0`}`}>
            <div className="w-[80vw] max-w-[250px] transition-all" style={{clipPath: `inset(${showBox ? `0% 0% 0% 0%` : showF ? `0% 100% 0% 0%` : `0% 0% 0% 100%`})`}}>
                <Logo boxColor={showF?`#171717`:`#FFFFFF`} letterColor={!showF?`#171717`:`#FFFFFF`} showF={showF}/>
            </div>
        </div>
    );
}
