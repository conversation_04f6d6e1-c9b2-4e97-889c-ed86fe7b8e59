'use client'

import { useEffect, useState } from "react";
import Logo from "../debut/_components/Logo";


export default function SiteIntro() {
    const [showF, setShowF] = useState(true);
    const [show, setShow] = useState(true);
    const [showBox, setShowBox] = useState(false);
    useEffect(() => {
        const timer = setTimeout(() => {
        setShowBox(true);
        }, 200);
        const timer2 = setTimeout(() => {
        setShowF(false);
        }, 1000);
        const timer4 = setTimeout(() => {
        setShowBox(false);
        }, 2300);
        const timer3 = setTimeout(() => {
        setShow(false);
        }, 2500);
        return () => {clearTimeout(timer);clearTimeout(timer2);clearTimeout(timer3);clearTimeout(timer4);}
    }, []);

    return (
        <div className={`fixed pointer-events-none z-50 transition-all duration-1000 ${show? `opacity-100` : `opacity-0`}`}
             style={{
                 top: '-10vh',
                 left: '-10vw',
                 width: '120vw',
                 height: '120vh'
             }}>
            {/* Solid blurred background that extends beyond viewport */}
            <div
                className="absolute inset-0 bg-white/90 blur-3xl"
                style={{
                    transform: 'scale(1.2)',
                    transformOrigin: 'center'
                }}
            />

            {/* Content container positioned back to viewport */}
            <div className="absolute flex justify-center items-center"
                 style={{
                     top: '10vh',
                     left: '10vw',
                     width: 'calc(100vw - 20vw)',
                     height: 'calc(100vh - 20vh)'
                 }}>
                <div className="w-[80vw] max-w-[250px] transition-all" style={{clipPath: `inset(${showBox ? `0% 0% 0% 0%` : showF ? `0% 100% 0% 0%` : `0% 0% 0% 100%`})`}}>
                    <Logo boxColor={showF?`#171717`:`#FFFFFF`} letterColor={!showF?`#171717`:`#FFFFFF`} showF={showF}/>
                </div>
            </div>
        </div>
    );
}
