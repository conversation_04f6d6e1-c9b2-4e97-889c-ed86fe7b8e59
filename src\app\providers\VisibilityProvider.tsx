// VisibilityContext.tsx
'use client';

import React, { createContext, useContext, useEffect, useRef, useState, type ReactNode } from 'react';

interface VisibilityContextType {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  toggle: () => void;
  staggerDelay?: number;
}

const VisibilityContext = createContext<VisibilityContextType | undefined>(undefined);

export const useVisibility = () => {
  const context = useContext(VisibilityContext);
  if (!context) throw new Error('useVisibility must be used within a VisibilityProvider');
  return context;
};

interface ProviderProps {
  children: ReactNode;
  initialVisible?: boolean;
  staggerDelay?: number;
}

export const VisibilityProvider = ({
  children,
  initialVisible = true,
  staggerDelay = 0
}: ProviderProps) => {
    const [isVisible, setVisible] = useState(initialVisible);
    const providerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        // Only run once when the component mounts
        if (!providerRef.current) return;

        const firstChild = providerRef.current.firstElementChild as HTMLElement | null;
        if (!firstChild) return;

        // Create the observer
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry?.isIntersecting) {
                    setVisible(true);
                    observer.disconnect();
                }
            },
            {
                threshold: 0.1,
                rootMargin: '-35% 0px -35% 0px' // This creates a smaller trigger area
            }
        );

        // Start observing
        observer.observe(firstChild);

        // Cleanup function
        return () => observer.disconnect();
    }, []);

    const value: VisibilityContextType = {
        isVisible,
        show: () => setVisible(true),
        hide: () => setVisible(false),
        toggle: () => setVisible(v => !v),
        staggerDelay
    };

    return (
        <VisibilityContext.Provider value={value}>
            <div ref={providerRef} style={{ display: 'contents' }}>
                {children}
            </div>
        </VisibilityContext.Provider>
    );
};
