"use client";

import { useEffect, useState, useRef } from "react";
import type { FormEvent } from "react";
import Logo from "./_components/Logo";
import CountdownTimer from "./_components/CountdownTimer";
import BackgroundGlow from "./_components/BackgroundGlow";
import Image from "next/image";
import Link from "next/link";
import { api } from "~/trpc/react";

export default function Debut() {
  // Launch date: June 29th, 2025
  const launchDate = new Date("2025-06-29T15:00:00Z");
  
  const [isHero, setIsHero] = useState(true);
  const [showF, setShowF] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const totalSlides = 3; // Total number of slides
  
  // Email form state
  const [email, setEmail] = useState("");
  const [formStatus, setFormStatus] = useState<{
    type: "success" | "error" | "idle";
    message: string;
  }>({ type: "idle", message: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Newsletter signup mutation
  const signupMutation = api.newsletter.signup.useMutation({
    onSuccess: (success) => {
      setFormStatus({
        type: "success",
        message: `${success.message ?? "Thank you for registering."} Follow our socials for more updates.`,
      });
      setEmail("");
      setIsSubmitting(false);
    },
    onError: (error) => {
      setFormStatus({
        type: "error",
        message: error.message || "Failed to register. Please try again.",
      });
      setIsSubmitting(false);
    },
  });

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!email) {
      setFormStatus({
        type: "error",
        message: "Please enter your email address.",
      });
      return;
    }
    
    setIsSubmitting(true);
    setFormStatus({ type: "idle", message: "" });
    signupMutation.mutate({ email });
  };

  useEffect(() => {
    const handleScroll = () => {
      if (scrollContainerRef.current) {
        // Check if at the top for hero state
        setIsHero(scrollContainerRef.current.scrollTop < window.innerHeight / 4);
        
        // Update current slide based on scroll position
        const containerHeight = scrollContainerRef.current.clientHeight;
        const scrollPosition = scrollContainerRef.current.scrollTop;
        const newSlideIndex = Math.min(
          Math.floor(scrollPosition / containerHeight + 0.5),
          totalSlides - 1
        );
        
        if (newSlideIndex !== currentSlide) {
          setCurrentSlide(newSlideIndex);
        }
      }
    };
    
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [currentSlide]);

  useEffect(() => {
    setTimeout(() => {
      setShowF(false);
    }, 1000);
  }, [setShowF]);

  // Simple scroll to section function for navigation dots
  const scrollToSection = (index: number) => {
    if (scrollContainerRef.current) {
      const containerHeight = scrollContainerRef.current.clientHeight;
      scrollContainerRef.current.scrollTo({
        top: index * containerHeight,
        behavior: 'smooth'
      });
    }
  };

  return (
      <main ref={scrollContainerRef} className="h-screen w-screen overflow-y-scroll snap-y snap-mandatory [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        {/* Fixed Navigation */}
        <div className={`fixed w-full h-[25vw] flex z-50 transition-all duration-500 ease-out justify-center pointer-events-none max-h-[200px] ${isHero ? 'h-[25vw] top-1/3' : 'top-0 h-[75px] backdrop-blur-md p-4'}`}>
          <Logo boxColor={!isHero ? "transparent" : "#FFFFFF"} letterColor={!isHero ? "#FFFFFF" : "#000000"} transitionDuration="0.5s" showF={showF}/>
        </div>

        {/* Instagram Link */}
        <Link 
          href="https://instagram.com/bx9series" 
          target="_blank" 
          rel="noopener noreferrer"
          className={`
            fixed bottom-6 right-6 z-50 
            w-12 h-12 bg-gradient-purple-pink rounded-full
            flex items-center justify-center
            transition-all duration-500 ease-out
            ${isHero ? 'translate-x-[200%] opacity-0' : 'translate-x-0 opacity-100'}
            hover:scale-110
          `}
          aria-label="Follow us on Instagram"
        >
          <svg 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
            className="text-white"
          >
            <path 
              d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" 
              fill="currentColor"
            />
          </svg>
        </Link>
        
        {/* Hero Section */}
        <section className="h-screen w-screen snap-start flex items-center justify-center relative overflow-hidden text-white">
          <div className="absolute inset-0 z-0">
            <BackgroundGlow />
          </div>
          <div className="h-full w-full flex flex-col justify-end items-center z-10">
            <div className={`${isHero ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500 ease-out sm:w-1/2 max-w-4xl h-1/2 bottom-0 flex flex-col justify-center items-center z-10`}>
              <CountdownTimer launchDate={launchDate} />
            </div>
          </div>
        </section>

        {/* Global Launch Event Section */}
        <section className="h-screen w-screen snap-start flex items-center justify-center p-4 relative overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image 
              src="/img/Pikiao.jpg" 
              alt="Launch Event Background" 
              fill 
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/60"></div>
          </div>
          <div className={`w-full max-w-4xl flex flex-col justify-center items-center z-10 transition-all duration-750 ease-out ${currentSlide === 1 ? 'opacity-100' : 'opacity-0 translate-y-1/2'}`}>
            <h1 className="text-2xl md:text-4xl uppercase font-primary mb-4 bg-gradient-purple-pink bg-clip-text text-white text-center">A NEW BREED OF BOXING IS HERE</h1>
            <h2 className="text-2xl md:text-xl uppercase font-secondary mb-4 bg-gradient-purple-pink bg-clip-text text-white text-center">New Zealand&apos;s best boxing athletes go head to head to claim the no. 1 spot</h2>
            <h2 className="text-2xl md:text-xl uppercase font-secondary mb-4 bg-gradient-purple-pink bg-clip-text text-white text-center">step in. stand out. get paid</h2>
          </div>
        </section>

        {/* Stay Updated Section */}
        <section className="h-screen w-screen snap-start flex items-center justify-center p-4 relative overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image 
              src="/img/Michael Isaac Wrapping-3.jpg" 
              alt="Stay Updated Background" 
              fill 
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/60"></div>
          </div>
          <div className="w-full max-w-3xl h-full flex items-center z-10">
            <div className={`w-full p-6 rounded-lg border border-purple-dark/20 bg-black/50 backdrop-blur-md transition-all duration-750 ease-out ${currentSlide === 2 ? 'opacity-100' : 'opacity-0 translate-y-1/2'}`}>
              <h2 className="text-2xl md:text-3xl font-primary mb-4 bg-gradient-orange-yellow bg-clip-text text-white">
                REGISTER INTEREST
              </h2>
              <p className="text-gray-300 mb-6 font-secondary">
                Athletes and fans sign up here to register your interest and stay up to date with the latest BX-9 news.
              </p>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                {formStatus.type != "success" &&
                  <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                    <div className="flex-1">
                      <input 
                        type="email" 
                        placeholder="Your email address"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={isSubmitting}
                        className="w-full px-4 py-3 text-white bg-black/70 border border-red-dark/30 rounded-md focus:outline-none focus:border-red-light transition-colors font-secondary"
                      />
                    </div>
                    <button 
                      type="submit"
                      disabled={isSubmitting}
                      className={`px-6 py-3 bg-gradient-red-blue text-white font-primary rounded-md transition-opacity ${isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:opacity-90'}`}
                    >
                      {isSubmitting ? 'REGISTERING...' : 'REGISTER'}
                    </button>
                  </div>
                }
                
                {formStatus.message && (
                  <div className={`mt-3 text-sm text-white font-primary`}>
                    {formStatus.message}
                  </div>
                )}
              </form>
            </div>
          </div>
        </section>

        {/* Section Navigation Dots */}
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 flex space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => scrollToSection(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                currentSlide === index 
                  ? 'bg-white scale-110' 
                  : 'bg-white/30 hover:bg-white/50'
              }`}
              aria-label={`Go to section ${index + 1}`}
            />
          ))}
        </div>
      </main>
  );
}







