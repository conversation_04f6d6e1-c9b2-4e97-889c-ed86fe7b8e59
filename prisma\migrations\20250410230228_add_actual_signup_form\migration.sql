-- CreateTable
CREATE TABLE "signup" (
    "id" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "bio" TEXT,
    "instagram" TEXT,
    "tiktok" TEXT,
    "gymName" TEXT NOT NULL,
    "coachName" TEXT NOT NULL,
    "coachPhoneNumber" TEXT NOT NULL,
    "coachEmail" TEXT NOT NULL,
    "region" TEXT NOT NULL,
    "weightClass" TEXT NOT NULL,
    "fightRecords" TEXT NOT NULL,
    "joinReason" TEXT NOT NULL,
    "mediaFiles" TEXT[],
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "signup_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "signup_email_key" ON "signup"("email");
