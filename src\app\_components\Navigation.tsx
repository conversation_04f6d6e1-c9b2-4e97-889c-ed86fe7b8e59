'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Logo from '../debut/_components/Logo';
import { register } from 'module';

interface NavigationProps {
  forceVariant?: 'primary' | 'secondary';
  register?: boolean;
}

export default function Navigation({ forceVariant, register }: NavigationProps) {
  const [currentVariant, setCurrentVariant] = useState<boolean>(true);

  useEffect(() => {
    if (forceVariant=='primary') {
      setCurrentVariant(true);
      return;
    }

    // Create an observer to detect sections with bg-primary
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const hasPrimaryBg = entry.target.classList.contains('bg-primary');
            setCurrentVariant(hasPrimaryBg);
          }
        });
      },
      {
        threshold: [0, 0.01], // Detect even minimal intersection
        rootMargin: '-0px 0px -99% 0px', // Only observe the top 80px of the viewport
        root: null
      }
    );

    // Observe all sections
    document.querySelectorAll('section').forEach((section) => {
      observer.observe(section);
    });

    return () => observer.disconnect();
  }, [forceVariant]);

  const navItems = [
    { label: 'SEMI-PRO', href: '/Semi-Pro' },
  ];

  const logoColors = {
    primary: {
      box: '#ffffff',
      letter: '#000000',
    },
    secondary: {
      box: '#000000',
      letter: '#ffffff',
    },
  };

  return (
    <>
      {/* Desktop Navigation */}
      <nav className={`fixed flex w-full max-w-[100vw] p-6 bg-transparent justify-between items-center z-40 text-${currentVariant} transition-all duration-300`}>
        <div className="flex flex-wrap justify-start items-center gap-6">
          <Link href="/" className="w-24 h-7 relative overflow-hidden">
            <Logo 
              boxColor={logoColors[!currentVariant?'primary':'secondary'].box} 
              letterColor={logoColors[!currentVariant?'primary':'secondary'].letter} 
              showF={false}
            />
          </Link>
          {navItems.map((item) => (
            <Link
              key={item.label}
              href={item.href}
              className={`uppercase leading-3 hidden md:block text-${!currentVariant?'primary':'secondary'}`}
            >
              <h6>{item.label}</h6>
            </Link>
          ))}
        </div>
        <div className='hidden hover:bg-primary hover:bg-secondary bg-primary bg-secondary text-primary text-secondary hover:text-primary hover:text-secondary'></div>
        {register ? (<Link
          href="#register"
          className={`px-4 py-3 hover:bg-${currentVariant?'primary':'secondary'} hover:text-${!currentVariant?'primary':'secondary'} text-${currentVariant?'primary':'secondary'} leading-tight bg-${!currentVariant?'primary':'secondary'} flex justify-center items-center transition-all duration-300`}
        >
          <span>
            REGISTER
          </span>
        </Link>) : (<Link
          href="https://www.eventfinda.co.nz/2025/bx-9-new-zealand-boxing-series/auckland/manukau-city" target="_blank"
          className={`px-4 py-3 hover:bg-${currentVariant?'primary':'secondary'} hover:text-${!currentVariant?'primary':'secondary'} text-${currentVariant?'primary':'secondary'} leading-tight bg-${!currentVariant?'primary':'secondary'} flex justify-center items-center transition-all duration-300`}
        >
          <span>
            GET TICKETS
          </span>
        </Link>)}
      </nav>

      {/* Mobile Navigation commented out for brevity */}
    </>
  );
}
