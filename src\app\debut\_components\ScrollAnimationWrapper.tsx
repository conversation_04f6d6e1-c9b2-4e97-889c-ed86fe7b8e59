"use client";

import { useEffect, useRef, useState } from "react";
import { useScroll } from "./ScrollContext";

interface ScrollAnimationWrapperProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number; // Percentage of element visible to trigger animation (0-1)
  animationDelay?: number; // Delay in ms
  id?: string; // Optional ID for targeting specific elements from parent components
}

export default function ScrollAnimationWrapper({
  children,
  className = "",
  threshold = 0.2,
  animationDelay = 0,
  id,
}: ScrollAnimationWrapperProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const { scrollY } = useScroll();

  // Use scrollY from context to trigger intersection check
  useEffect(() => {
    if (hasAnimated || !ref.current) return;

    const element = ref.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // Calculate how much of the element is in viewport
    const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
    const visiblePercentage = visibleHeight / rect.height;
    
    if (visiblePercentage > threshold) {
      if (animationDelay) {
        setTimeout(() => {
          setIsVisible(true);
          setHasAnimated(true);
        }, animationDelay);
      } else {
        setIsVisible(true);
        setHasAnimated(true);
      }
    }
  }, [scrollY, threshold, animationDelay, hasAnimated]);

  return (
    <div 
      ref={ref}
      className={className}
      data-animate={isVisible ? "visible" : "hidden"}
      data-animation-id={id}
    >
      {children}
    </div>
  );
} 