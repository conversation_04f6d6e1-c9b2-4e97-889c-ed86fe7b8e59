interface LogoProps {
  boxColor?: string;
  letterColor?: string;
  transitionDuration?: string;
  showF?: boolean;
}

export default function Logo({ 
  boxColor = "transparent", 
  letterColor = "#FFFFFF",
  transitionDuration = "0.3s",
  showF = false
}: LogoProps) {
  return (
    <svg className="h-full w-auto max-w-full" width="1000" height="289" viewBox="0 0 1000 289" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect 
        width="1000" 
        height="288.74" 
        fill={boxColor}
        style={{ transition: `fill ${transitionDuration} ease-in-out` }}
      />
      
      <g 
        fill={letterColor}
        style={{ transition: `fill ${transitionDuration} ease-in-out` }}
      >
        {/* B */}
        <path d="M320.53 184.07V185.97C320.53 205.91 313.83 219.82 300.41 227.71C287 235.59 262.98 239.54 228.35 239.54H47.25V49.2H222.91C256.63 49.2 280.42 52.42 294.29 58.85C308.16 65.29 315.09 77.21 315.09 94.61V96.51C315.09 111.19 311.24 121.93 303.53 128.73C295.83 135.53 283.01 139.56 265.06 140.83V143.55C285.19 144.46 299.45 147.95 307.88 154.02C316.31 160.09 320.52 170.11 320.52 184.06L320.53 184.07ZM202.79 98.96H128.83V124.25H209.6C219.02 124.25 225.37 123.44 228.63 121.8C231.89 120.17 233.52 117.27 233.52 113.1V112.01C233.52 107.11 231.52 103.72 227.54 101.81C223.56 99.9 216.3 98.95 203.79 98.95L202.79 98.96ZM200.62 163.13H128.83V189.78H200.62C216.21 189.78 226.5 188.87 231.49 187.06C236.47 185.25 238.97 181.8 238.97 176.73V175.91C238.97 170.84 236.61 167.44 231.89 165.72C227.17 164 217.03 163.13 200.89 163.13H200.62Z"/>
        {/* X */}
        <path 
          d="M532.09 141.65L643.31 239.54H532.09L476.34 185.15H473.08L414.89 239.54H312.37L424.41 140.83L320.8 49.19H430.38L478.78 97.05H481.78L532.36 49.19H637.05L532.09 141.64V141.65Z" 
        />
        {/* - */}
        <path 
          d="M678.66 169.66H583.48V119.35H678.66V169.66Z"
        />
        {/* Define the clipping mask */}
        <defs>
          <clipPath id="f9-clip">
            <rect x="691.55" y="46.48" width="261.19" height="195.78" />
          </clipPath>
        </defs>
        {/* F/9 container with clip path */}
        <g clipPath="url(#f9-clip)">
          {/* F or 9 */}
          <path 
            d="M691.55 46.48H952.74V96.79H783.66V120.44H952.74V170.75H783.66V242.26H691.55V46.48Z"
            style={{ 
              transformOrigin: "822px 144px", 
              transform: `translateY(${showF ? 0 : 110}%)`,
              transition: `transform ${transitionDuration} cubic-bezier(0.680, -0.150, 0.265, 1.150)`
            }}          />
          <path 
            d="M797.75 46.48H838.81C880.32 46.48 909.69 53.78 926.91 68.37C944.12 82.96 952.74 106.4 952.74 138.66V149.81C952.74 182.62 943.9 206.23 926.22 220.64C908.55 235.05 879.14 242.26 837.98 242.26H808.34C782.42 242.26 761.07 239.81 744.31 234.92C727.54 230.02 715.26 223.59 707.46 215.61C699.67 207.64 694.14 197.49 690.87 185.15H773C775.35 187.87 779.61 189.78 785.78 190.86C791.94 191.95 803.36 192.49 820.04 192.49C836.72 192.49 849.04 191.27 857.29 188.82C865.54 186.37 871.03 182.84 873.74 178.22C876.46 173.59 877.82 166.85 877.82 157.96V154.16H875.1C867.66 164.49 841.29 169.66 795.97 169.66C777.66 169.66 762.29 168.89 749.88 167.35C737.46 165.81 727.4 163.68 719.7 160.96C711.99 158.24 706.06 154.39 701.88 149.4C697.71 144.42 694.95 139.16 693.59 133.63C692.24 128.1 691.55 121.08 691.55 112.56V111.47C691.55 88.27 700.44 71.63 718.2 61.57C735.96 51.51 762.7 46.48 798.42 46.48H797.75ZM858.66 97.06C851.05 96.16 839.26 95.7 823.31 95.7C807.36 95.7 795.58 96.16 787.97 97.06C780.35 97.97 775.33 99.42 772.87 101.41C770.42 103.4 769.21 106.48 769.21 110.65V114.46C769.21 118.63 770.43 121.71 772.87 123.71C775.32 125.71 780.35 127.15 787.97 128.06C795.58 128.97 807.36 129.42 823.31 129.42C839.26 129.42 851.05 128.97 858.66 128.06C866.28 127.15 871.3 125.71 873.75 123.71C876.2 121.72 877.42 118.64 877.42 114.46V110.65C877.42 106.48 876.2 103.4 873.75 101.41C871.3 99.42 866.28 97.97 858.66 97.06Z" 
            style={{ 
              transformOrigin: "822px 144px", 
              transform: `translateY(${showF ? -110 : 0}%)`,
              transition: `transform ${transitionDuration} cubic-bezier(0.680, -0.150, 0.265, 1.150)`
            }}            
          />
        </g>
      </g>
    </svg>
  );
} 
