'use client'
import { useEffect, useRef, useState } from "react";

interface EventTableRowProps {
  children: React.ReactNode;
  rounds: string;
  index: number;
  isMainEvent?: boolean;
  isVisible: boolean
}

export default function EventTableRow({ children, rounds, index, isMainEvent, isVisible }: EventTableRowProps) {
  return (
    <tr style={{ transform: `translateY(${isVisible ? '0' : '100%'})`, opacity: isVisible ? 1 : 0, transition: 'all 0.5s ease-out', transitionDelay:  (11-index)*0.1+'s' }}>
      <td className={`border-blue-dark  ${isMainEvent ? 'bg-blue-dark' : 'bg-transparent'} border px-2 md:px-4 ${isMainEvent ? 'text-primary' : 'text-blue-dark'} text-center w-15`}>
        <h6>{index}</h6>
      </td>
      <td className={`px-2 border-blue-dark ${isMainEvent ? 'bg-blue-light' : 'bg-transparent'} border-y`}>
        <div className="flex items-center gap-2">
          <p className={`${isMainEvent ? 'text-primary' : 'text-blue-dark'} text-main text-nowrap uppercase py-1`}>{children}</p>
        </div>
      </td>
      <td className={`px-4 border-blue-dark ${isMainEvent ? 'bg-blue-light' : 'bg-transparent'} border ${isMainEvent ? 'text-primary' : 'text-blue-dark'} h6l uppercase text-right whitespace-nowrap w-0`}>
        {rounds}
      </td>
    </tr>
  );
}
