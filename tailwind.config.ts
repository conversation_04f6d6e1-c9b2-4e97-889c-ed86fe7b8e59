import type { Config } from "tailwindcss";
import defaultTheme from "tailwindcss/defaultTheme";

const config: Config = {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  darkMode: "class",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // Primary Colors
        white: '#FFFFFF',
        black: '#000000',

        // Secondary Colors
        red: {
          light: '#ED1A27',
          dark: '#470707',
        },
        blue: {
          light: '#3355FF',
          dark: '#0F163F',
        },

        // Tertiary Colors
        orange: {
          light: '#FF7936',
          dark: '#612C05',
        },
        yellow: {
          light: '#FFED2C',
          dark: '#3C3A00',
        },
        green: {
          light: '#7EFF33',
          dark: '#204211',
        },
        turquoise: {
          light: '#33FF7C',
          dark: '#0D3F13',
        },
        'baby-blue': {
          light: '#33D9FF',
          dark: '#043644',
        },
        purple: {
          light: '#8233FF',
          dark: '#251540',
        },
        pink: {
          light: '#FF647D',
          dark: '#4E1122',
        },
      },
      backgroundImage: {
        'gradient-red-blue': 'linear-gradient(to right, #ED1A27, #3355FF)',
        'gradient-purple-pink': 'linear-gradient(to right, #8233FF, #FF647D)',
        'gradient-green-turquoise': 'linear-gradient(to right, #7EFF33, #33FF7C)',
        'gradient-orange-yellow': 'linear-gradient(to right, #FF7936, #FFED2C)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-out',
        'fade-in-up': 'fadeInUp 0.7s ease-out',
        'slide-in-left': 'slideInLeft 0.5s ease-out',
        'slide-in-right': 'slideInRight 0.5s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      fontFamily: {
        // Primary font - PP Neue Corp Medium (Regular weight)
        secondary: ["var(--font-neuecorp-medium)", ...defaultTheme.fontFamily.sans],
        // Secondary font - PP Neue Corp Ultrabold (Bold weight)
        primary: ["var(--font-neuecorp-ultrabold)", ...defaultTheme.fontFamily.sans],
        // Maintain compatibility with existing font-sans
        sans: ["var(--font-sans)", ...defaultTheme.fontFamily.sans],
      },
    },
  },
  plugins: [],
} as const;

export default config; 