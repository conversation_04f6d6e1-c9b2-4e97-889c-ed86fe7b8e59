import Image from "next/image";
import RegionTag from "../_components/RegionTag";
import Navigation from "../_components/Navigation";
import Link from "next/link";
import SignupForm from "../_components/SignupForm";
import Logo from "../debut/_components/Logo";
import EventTable from "../_components/EventTable";
import BoxReveal from "../_components/BoxReveal";
import { VisibilityProvider } from "../providers/VisibilityProvider";

export default function Home() {
  const register = false;
  return (<>
    <Navigation register={register}/>
    <main className="overflow-hidden">

      <section id="leaderboard" className="px-6 md:px-12 py-20 bg-red-light min-h-[100vh] w-full flex justify-center gap-12">
              <div className="w-full max-w-[2000px] relative flex flex-row flex-wrap items-stretch justify-center gap-12">
          
      <VisibilityProvider initialVisible={false} staggerDelay={100}>
        <div className="grow min-w-fit">
            <div className="grow h-full relative flex flex-col justify-between gap-12">
              <div className="flex flex-col justify-between gap-2">
                <h3 className="text-primary uppercase z-10">The best in nz go head to head</h3>
                <h5 className="text-primary uppercase z-10">Semi-Pro Series</h5>
              </div>
              <div>
                <h4 className="text-red-dark uppercase z-10">one National leaderboard<br />per weight division</h4>
                <h6 className="text-red-dark">streamlined weight divisions for males & females</h6>
              </div>
              <div className="w-full">
                <BoxReveal animation="fade-up" staggerIndex={1}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Flyweight</h5>
                    <p className="text-large text-white">57KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={2}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Bantamweight</h5>
                    <p className="text-large text-white">62KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={3}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Featherweight</h5>
                    <p className="text-large text-white">66KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={4}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Lightweight</h5>
                    <p className="text-large text-white">70KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={5}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Welterweight</h5>
                    <p className="text-large text-white">76KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={6}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Middleweight</h5>
                    <p className="text-large text-white">80KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={7}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Light Heavyweight</h5>
                    <p className="text-large text-white">68KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={8}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Cruiserweight</h5>
                    <p className="text-large text-white">95KG</p>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-up" staggerIndex={9}>
                  <div className="w-full flex justify-between border-b p-1">
                    <h5 className="text-red-dark pt-1">Heavyweight</h5>
                    <p className="text-large text-white">100KG+</p>
                  </div>
                </BoxReveal>
              </div>
            </div>
          </div>
        </VisibilityProvider>
        <VisibilityProvider initialVisible={false} staggerDelay={100}>
          <div className="grow min-w-fit max-w-xl">
            
            <BoxReveal animation="clip-right" staggerIndex={0}>
            <div className="w-full min-w-fit p-6 relative border-red-dark border flex flex-col gap-6">
              <div className="h-7"><Logo boxColor="#470707" letterColor="#ED1A27" showF={false}/></div>
              <h3 className="text-primary justify-start uppercase">LEADER<br className="md:hidden"/>BOARD</h3>
              <div className="self-stretch w-0 min-w-full inline-flex justify-center items-center gap-2 overflow-hidden text-nowrap">
                <div className="bg-red-dark z-10 w-fit p-1">
                  <h6 className="text-red-light">LIGHTWEIGHTS</h6>
                </div>
                <div className="bg-primary z-10 w-fit p-2.5">
                  <h6 className="text-red-dark">MIDDLEWEIGHTS</h6>
                </div>
                <div className="bg-red-dark z-10 w-fit p-1">
                  <h6 className="text-red-light">LIGHT HEAVYWEIGHTS</h6>
                </div>
              </div>
              
              <div className="flex gap-6">
                  <h5 className="text-primary">Male</h5>
                  <h5 className="text-red-dark">Female</h5>
              </div>
                <table className="w-fit min-w-full table-auto [border-spacing:0_0.2rem] border-separate">
                  <tbody>
                    {Array.from({ length: 9 }).map((_, i) => (
                      <BoxReveal key={i} animation="fade-up" staggerIndex={i+5}>
                        <tr>
                          <td className={`${i<3?'bg-red-dark ':''}border-red-dark border px-2 md:px-4 w-0 text-white text-center h6`}>{i+1}</td>
                          <td className="px-2 border-red-dark border-y">
                            <div className="flex items-center gap-2">
                              <div className="h-8 my-1 aspect-square relative rounded-full bg-white">
                                <Image src="/img/face.png" alt="Example Athlete Headshot" fill className="object-cover rounded-full" />
                              </div>
                              <p className="text-main text-primary text-nowrap">Athlete Name</p>
                            </div>
                          </td>
                          <td className="px-4 border-red-dark border text-red-dark h6l uppercase text-right whitespace-nowrap w-0">
                            {(((Math.random() + 100-(i*10)))/13).toFixed(0)} Pts
                          </td>
                        </tr>
                      </BoxReveal>
                    ))}
                  </tbody>
                </table>
            </div>
            </BoxReveal>
          </div>
      </VisibilityProvider>
        </div>
        
      </section>
      <section id="earn" className="relative w-full flex flex-col justify-center min-h-[80vh] items-center px-12 py-40 gap-12 overflow-clip">
        <Image src="/img/Michael Isaac Wrapping-3.jpg" alt="Blurred Background" fill className="object-fill blur-[100px]" />
        <h2 className="uppercase w-full md:w-2/3 max-w-6xl text-primary z-10 text-center leading-[1]">The first-ever
        POINT SYSTEM THAT <span className="text-green-light">Guarantees</span> PAY</h2>
        <h5 className="uppercase w-full md:w-2/3 max-w-6xl text-primary z-10 text-center">WIN, LOSE or DRAW PER BOUT</h5>
      </section>
      <section id="points" className="bg-green-light min-h-[80vh] h-fit w-full flex flex-wrap justify-center relative gap-12 px-6 md:px-12 py-20">
      <div className="w-full max-w-[2000px] relative flex flex-row flex-wrap items-stretch justify-between gap-12">
          <div className="grow md:max-w-1/2 relative flex flex-col justify-between gap-2">
            <div className="gap-4 flex flex-col py-4 text-primary">
              <h1 className="uppercase z-10">Introducing</h1>
              <h3 className="uppercase z-10">the all new 3 POINT PERFORMANCE SYSTEM</h3>
            </div>
            <h4 className="text-green-dark">EVERY POINT EARNED GETS ADDED TO YOUR TALLY ON THE NATIONAL LEADERBOARD</h4>
          </div>
      <VisibilityProvider initialVisible={false} staggerDelay={200}>
          <div className="grow max-w-2xl flex items-end">
                <BoxReveal animation="clip-right" staggerIndex={0}>
            <div className="w-full min-w-full p-2 md:p-6 relative border-blue-dark border flex flex-col gap-6">
                  <table className="w-fit min-w-full table-auto [border-spacing:0_0.2rem] border-separate text-green-dark">
                    <tbody>
                    <BoxReveal animation="fade-up" staggerIndex={2}>
                      <tr>
                        <td className={`bg-primary px-2 md:px-4 w-0 text-center text-nowrap`}><h6>3 PTS</h6></td>
                        <td className="px-2 relative">
                          <span className="text-small text-nowrap uppercase py-1">WIN BY STOPPAGE IN R1</span>
                          <span className="absolute right-0 top-1/2 -translate-y-1/2 px-4 h6 uppercase text-right whitespace-nowrap">$3K</span>
                        </td>
                      </tr>
                      </BoxReveal>
                      <BoxReveal animation="fade-up" staggerIndex={3}>
                      <tr>
                        <td className={`bg-primary px-2 md:px-4 w-0 text-center text-nowrap`}><h6>2 PTS</h6></td>
                        <td className="px-2 relative">
                          <span className="text-small text-nowrap uppercase py-1">WIN BY STOPPAGE IN R2-3</span>
                          <span className="absolute right-0 top-1/2 -translate-y-1/2 px-4 h6 uppercase text-right whitespace-nowrap">$2K</span>
                        </td>
                      </tr>
                      </BoxReveal>
                      <BoxReveal animation="fade-up" staggerIndex={4}>
                      <tr>
                        <td className={`bg-primary px-2 md:px-4 w-0 text-center text-nowrap`}><h6>1 PTS</h6></td>
                        <td className="px-2 relative">
                          <span className="text-small text-nowrap uppercase py-1">WIN BY DECISION</span>
                          <span className="absolute right-0 top-1/2 -translate-y-1/2 px-4 h6 uppercase text-right whitespace-nowrap">$1K</span>
                        </td>
                      </tr>
                      </BoxReveal>
                      <BoxReveal animation="fade-up" staggerIndex={5}>
                      <tr>
                        <td className={`bg-green-dark px-2 md:px-4 w-0 text-center text-nowrap text-primary`}><h6>0 PTS</h6></td>
                        <td className="px-2 relative">
                          <span className="text-small text-nowrap uppercase py-1">LOSS/DRAW</span>
                          <span className="absolute right-0 top-1/2 -translate-y-1/2 px-4 h6 uppercase text-right whitespace-nowrap">$500</span>
                        </td>
                      </tr>
                      </BoxReveal>
                    </tbody>
                  </table>
            </div>
              </BoxReveal>
          </div>
      </VisibilityProvider>
        </div>
      </section>
      <section id="regions" className="px-6 md:px-12 py-20 bg-primary min-h-[100vh] w-full flex justify-center gap-12">
        <div className="w-full max-w-[2000px] relative flex flex-row flex-wrap items-stretch justify-center gap-12">
          <div className="grow min-w-fit">
              <h1 className="text-secondary uppercase z-10">Regions</h1>
              <h5 className="text-secondary uppercase z-10 mt-5">REGIONAL REPRESENTATION</h5>
              <p className="text-secondary text-large font-medium z-10 mt-6 wrap-normal w-0 min-w-full">Athletes will represent areas across New Zealand. Will you be repping your neighbourhood?</p>
          </div>
          <div className="grow min-w-fit max-w-2xl">
            <div className="w-full relative mt-16 px-10 md:px-40 -translate-x-1/9">
              {/* Map container */}
              <Image 
                src="/img/nz.svg" 
                alt="New Zealand regions map" 
                width={579} 
                height={819}
                className="w-full h-full"
              />
            
              {/* Location Tags */}
              <div className="w-full h-full absolute top-0 left-0 px-10 md:px-40">
                <div className="w-full h-full relative">
                
                  {/* Northland */}
                  <RegionTag 
                    name="Northland"
                    direction="right"
                    position={{ top: '8%', right: '38%' }}
                    tail={{ height: 5, width: 10 }}
                    color="orange"
                  />

                  {/* Auckland */}
                  <RegionTag 
                    name="Auckland"
                    direction="left"
                    position={{ top: '18%', left: '68%' }}
                    tail={{ height: 5, width: 23 }}
                    color="red"
                  />
                  
                  {/* Central North Island */}
                  <RegionTag 
                    name="Central\nNorth Island"
                    direction="right"
                    position={{ top: '25%', right: '25%' }}
                    tail={{ height: 5, width: 25 }}
                    color="baby-blue"
                  />
                  
                  {/* West North Island */}
                  <RegionTag 
                    name="West\nNorth Island"
                    direction="left"
                    position={{ top: '36%', left: '65%' }}
                    tail={{ height: 5, width: 28 }}
                    color="pink"
                  />
                  
                  {/* East North Island */}
                  <RegionTag 
                    name="East\nNorth Island"
                    direction="right"
                    position={{ top: '38%', right: '16%' }}
                    tail={{ height: 5, width: 18 }}
                    color="green"
                  />
                  
                  {/* Wellington */}
                  <RegionTag 
                    name="Wellington"
                    direction="right"
                    position={{ top: '51%', right: '30%' }}
                    tail={{ height: 5, width: 17 }}
                    color="blue"
                  />
                  
                  {/* Upper South Island */}
                  <RegionTag 
                    name="Upper\nSouth Island"
                    direction="left"
                    position={{ top: '55%', left: '50%' }}
                    tail={{ height: 5, width: 20 }}
                    color="turquoise"
                  />
                  
                  {/* Central South Island */}
                  <RegionTag 
                    name="Central\nSouth Island"
                    direction="right"
                    position={{ top: '70%', right: '50%' }}
                    tail={{ height: 5, width: 18 }}
                    color="purple"
                  />
                  
                  {/* Lower South Island */}
                  <RegionTag 
                    name="Lower South\nIsland"
                    direction="right"
                    position={{ top: '88%', right: '85%' }}
                    tail={{ height: 5, width: 30 }}
                    color="yellow"
                  />

                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section id="safety" className="w-full bg-red-light text-red-dark px-6 md:px-12 py-20 flex flex-col md:flex-row justify-center gap-12 text-wrap">
        <div className="grow w-full md:w-1/3 min-h-fit flex flex-col justify-between uppercase text-wrap">
          <h1><span className="text-primary">Safety</span> is our no.1 priority</h1>
        </div>
        <div className="grow w-full md:w-1/3 font-secondary">
          <div className="w-full aspect-video relative">
            <Image src="/img/Michael Isaac Wrapping-3.jpg" alt="Michael Isaac BX-F Athlete & Pro Fighter" fill className="object-cover" />
            <p className="text-large hidden sm:block z-20 absolute bottom-0 left-0 p-6 text-primary uppercase">Michael Isaac<br />BX-F Athlete & Pro Fighter</p>
          </div>
          <ul className="text-primary uppercase list-[square] pl-7 py-2 max-w-fit">
            <li><h5>MEDICAL CHECKS</h5></li>
            <li><h5>POLICE VETTING</h5></li>
            <li><h5>DR, NURSE & AMBULANCE ON SITE</h5></li>
          </ul>
        </div>
      </section>

      <footer className="bg-secondary text-primary p-6 md:p-12 flex flex-col gap-6 md:gap-12 text-large">

        <div className="flex justify-between">
          
        <div className="w-32 md:w-48 h-10 md:h-14 relative bg-primary">
            <Image 
              src="/img/BX-9.svg" 
              alt="BX-9 Logo" 
              fill 
              className="object-contain"
            />
          </div>
          <div className="flex justify-center flex-col items-center">
            <h6>Powered By</h6>
            <div className="w-30 md:w-48 h-10 md:h-14 relative">
              <Logo showF={true} boxColor="transparent" />
            </div>
          </div>
        </div>
        <div className="w-full">
          <h1 className="font-bold uppercase text-red-light">a new breed</h1>
          <h1 className="font-bold uppercasetext-primary">of boxing is here</h1>
        </div>
        <div className="flex justify-between text-nowrap flex-wrap flex-row gap-6">
          <div className="flex flex-col md:flex-row gap-6 flex-wrap">
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary/20 gap-4">
              <p className="text-primary/50 uppercase text-left">Sanctioned by PBC NZ</p>
              <Image 
                src="/img/pbc.png" 
                alt="PBC NZ Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary/20 gap-4">
              <p className="text-primary/50 uppercase text-left">Live streamed globally on</p>
              <Image 
                src="/img/csn.png" 
                alt="CSN Streaming Platform Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary gap-4">
              <p className="text-primary/50 uppercase text-left">Recorded on BoxRec<br/>as semi-pro (3 rounds)<br/>and pro (4+ rounds).</p>
              <Image 
                src="/img/boxrec.png" 
                alt="Boxrec Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 gap-4">
              <p className="text-primary/50 uppercase text-left">Need Help</p>
              <Link href="mailto:<EMAIL>" className="text-primary uppercase text-left"><EMAIL></Link>
            </div>
          </div>
          <Link href="https://www.duedropeventscentre.org.nz/" target="_blank"><Image 
                  src="/img/dewdrop.png" 
                  alt="Brand Logo"
                    height={100}
                    width={100}
                  className="h-full object-contain object-bottom-right"
                /></Link>
        </div>
      </footer>
    </main>
    </>
  );
}
