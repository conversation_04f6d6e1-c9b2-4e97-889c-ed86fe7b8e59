"use client";

import { useState, useEffect, useRef } from "react";
import { useScroll } from "./ScrollContext";

interface ScrollAnimationOptions {
  startOffset?: number; // Start animation when element is this many pixels from the top
  endOffset?: number; // End animation when element is this many pixels from the top
  startThreshold?: number; // Start animation when element is this percentage visible (0-1)
  endThreshold?: number; // End animation when element is this percentage visible (0-1)
  reverse?: boolean; // Whether animation should reverse when scrolling back up
  clamp?: boolean; // Whether to clamp progress between 0 and 1
}

// Main hook to get animation progress based on element position
export function useScrollAnimation<T extends HTMLElement = HTMLDivElement>(
  options: ScrollAnimationOptions = {}
): [React.RefObject<T>, number] {
  const {
    startOffset = 0,
    endOffset = 0,
    startThreshold = 0.2,
    endThreshold = 0.8,
    reverse = true,
    clamp = true,
  } = options;

  const elementRef = useRef<T>(null);
  const [progress, setProgress] = useState(0);
  const { scrollY } = useScroll();

  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    // Calculate start and end positions
    const startPosition = windowHeight * (1 - startThreshold) + startOffset;
    const endPosition = windowHeight * (1 - endThreshold) + endOffset;
    const totalDistance = endPosition - startPosition;

    // Calculate current progress
    let currentProgress = 0;
    if (totalDistance !== 0) {
      currentProgress = (startPosition - rect.top) / totalDistance;
      
      if (clamp) {
        currentProgress = Math.max(0, Math.min(1, currentProgress));
      }
      
      if (!reverse && currentProgress < 0) {
        currentProgress = 0;
      }
    }

    setProgress(currentProgress);
  }, [scrollY, startOffset, endOffset, startThreshold, endThreshold, reverse, clamp]);

  return [elementRef as React.RefObject<T>, progress];
}

// Utility hook to get element visibility percentage
export function useElementVisibility<T extends HTMLElement = HTMLDivElement>(): [React.RefObject<T>, number] {
  const elementRef = useRef<T>(null);
  const [visiblePercentage, setVisiblePercentage] = useState(0);
  const { scrollY } = useScroll();

  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // Calculate how much of the element is in viewport
    const visibleTop = Math.max(0, rect.top);
    const visibleBottom = Math.min(windowHeight, rect.bottom);
    const visibleHeight = Math.max(0, visibleBottom - visibleTop);
    const percentage = visibleHeight / rect.height;
    
    setVisiblePercentage(percentage);
  }, [scrollY]);

  return [elementRef as React.RefObject<T>, visiblePercentage];
}