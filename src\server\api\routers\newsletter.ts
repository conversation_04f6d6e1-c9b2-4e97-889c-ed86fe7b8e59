import { z } from "zod";
import { createTR<PERSON>Router, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Prisma } from "@prisma/client";

export const newsletterRouter = createTRPCRouter({
  signup: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const signup = await ctx.db.newsletterSignup.create({
          data: {
            email: input.email,
          },
        });
        return { success: true, signup };
      } catch (error: unknown) {
        // Check if error is due to unique constraint violation
        if (
          error instanceof Prisma.PrismaClientKnownRequestError && 
          error.code === 'P2002'
        ) {
          return { success: true, message: `You've already registered!` };
        }
        
        console.error("Newsletter signup error:", error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to register email. Please try again later.',
        });
      }
    }),
}); 