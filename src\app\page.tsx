import Image from "next/image";
import RegionTag from "./_components/RegionTag";
import Navigation from "./_components/Navigation";
import Link from "next/link";
import SignupForm from "./_components/SignupForm";
import Logo from "./debut/_components/Logo";
import EventTable from "./_components/EventTable";
import BoxReveal from "./_components/BoxReveal";
import { VisibilityProvider } from "./providers/VisibilityProvider";
import BackgroundGlow from "./debut/_components/BackgroundGlow";
import CountdownTimer from "./debut/_components/CountdownTimer";

export default function Home() {

  const launchDate = new Date("2025-06-29T15:00:00Z");
  const register = false;
  return (<>
    <Navigation register={register} />
    <main className="overflow-hidden">
      <section id="hero" className="relative w-full min-h-screen flex flex-col justify-end px-6 md:px-12 py-20 gap-6">
        <div className="absolute inset-0 bg-gradient-to-r from-secondary/50 to-transparent z-10" />
        <Image src="/img/titus.jpg" alt="Titus Proctor, B<PERSON>-<PERSON>, Pro Muay Thai Fighter, Multiple NZ & World Champ" fill className="absolute inset-0 object-cover object-top" />
        <span className="text-large hidden sm:block z-20 md:absolute md:bottom-20 md:right-12 text-primary text-right -mb-4 uppercase">Titus Proctor<br />BX-F Athlete<br />Pro Muay Thai Fighter<br />Multiple NZ & World Champ</span>
        <h1 className="text-primary z-10"><span className="text-red-light">A NEW BREED<br /></span>OF BOXING<br />IS HERE</h1>
        <h4 className="text-primary z-10">DUE DROP EVENTS CENTRE MANUKAU</h4>
        <h4 className="text-primary z-10">SUNDAY 29 JUNE 2025</h4>
        <Link
          href="https://www.eventfinda.co.nz/2025/bx-9-new-zealand-boxing-series/auckland/manukau-city" target="_blank" className="bg-red-light z-10 w-fit p-2">
          <h4 className="text-red-dark">TICKETS ON SALE NOW</h4>
        </Link>
      </section>
      <section id="about" className="bg-blue-light w-full flex min-h-[70vh] flex-wrap justify-center relative gap-12">
        <div className="px-6 md:px-12 py-10 md:py-20 w-full flex justify-center">
          <div className="max-w-[2000px] w-full">
            <div className="w-full md:w-[50vw] max-w-[1400px] h-full flex flex-col justify-between">
              <h4 className="text-primary uppercase z-10 leading-8 text-balance">
                BX-9 is a professional boxing series designed to develop rising talent and pave the way to World Championships.</h4>
              <br />
              <h4 className="text-primary uppercase z-10 leading-8 text-balance">
                Featuring 3x3-minute semi-pro bouts and full-length pro contests up to 12 rounds, it’s a complete platform for the next generation of champions.
              </h4>


            </div>
          </div>
        </div>
        <div className="right-0 aspect-square w-full h-full md:w-1/3 relative md:absolute md:right-0 md:top-0 bg-white/30">
          <Image src="/img/zach.jpg" alt="Zack Bennett BX-F Athlete & Pro Fighter" fill className="object-cover" />
          <p className="text-large hidden sm:block z-20 absolute bottom-0 left-0 p-6 text-primary uppercase">Zack Bennett<br />BX-F Athlete & Pro Fighter</p>
        </div>
      </section>
      <section id="ruleset" className="bg-primary w-full flex flex-wrap justify-center relative gap-12 px-6 md:px-12 py-20">
        <div className="bg-primary w-full max-w-[2000px] flex flex-wrap justify-between relative gap-4">
          <h1 className="text-blue-dark uppercase z-10 max-w-[50vw]">PRO BOXING RULESET</h1>
          <div className="max-w-xl h-fit relative flex items-center gap-4">
            <div className="aspect-square h-[5rem] relative">
              <Image src="/img/pbc.png" alt="PBC NZ Logo" fill className="object-scale-down" />
            </div>
            <div className="h-full w-full flex items-center max-w-[260px]">
              <h6>SANCTIONED BY<br />
                <span className="text-blue-light">Professional Boxing Commission of NZ</span></h6>
            </div>
          </div>
        </div>
        <VisibilityProvider initialVisible={false} staggerDelay={200}>

          <div className="w-full max-w-[2000px] relative flex flex-row flex-wrap items-stretch justify-center gap-12">
            <div className="grow min-w-fit">
              <div className="grow h-full max-w-xl relative flex flex-col justify-between gap-2">
                <BoxReveal animation="fade-left" staggerIndex={0}>
                  <div className="gap-4 flex flex-col py-4">
                    <h4 className="text-blue-dark uppercase z-10">SEMI-PRO VS pro?</h4>
                    <h5 className="text-blue-dark uppercase z-10">Put simply, semi-pro defines a 3x3  PRO RULES bout. Semi-pro athletes with a <span className="text-blue-light">3+ win streak</span> who reach the top of their weight division qualify for 6+ round main event pro bouts.</h5>
                  </div>
                </BoxReveal>
                <BoxReveal animation="fade-left" staggerIndex={1}>
                  <div className="text-blue-dark mt-15 uppercase z-10 w-full">
                    {/* <h1 className="relative w-full whitespace-nowrap text-[min(11vw,100px)] leading-[min(11vw,100px)] md:text-[min(7.5vw,100px)] md:leading-[min(7.5vw,100px)]"><span className="text-blue-dark">9</span> MINS<span className="font-secondary text-[25%] absolute -top-1/4">(3x3)</span></h1> */}
                    {/* <h1 className="relative w-full whitespace-nowrap text-[min(11vw,100px)] leading-[min(11vw,100px)] md:text-[min(7.5vw,100px)] md:leading-[min(7.5vw,100px)]"><span className="text-blue-dark">9</span> BOUTS<span className="font-secondary text-[25%] absolute -top-1/4">(per event)</span></h1> */}
                    <h1 className="relative w-full whitespace-nowrap text-[min(9vw,80px)] leading-[min(11vw,100px)] md:text-[min(4.5vw,100px)] md:leading-[min(7.5vw,100px)]"><span className="text-blue-light">9</span> Minutes<span className="font-secondary text-[25%] absolute -top-1/4">(Of Action)</span></h1>
                    <h1 className="relative w-full whitespace-nowrap text-[min(9vw,80px)] leading-[min(11vw,100px)] md:text-[min(4.5vw,100px)] md:leading-[min(7.5vw,100px)]"><span className="text-blue-light">9</span> Weight<span className="font-secondary text-[25%] absolute -top-1/4">(Divisions)</span></h1>
                  </div>
                </BoxReveal>
              </div>
            </div>
            <div className="grow max-w-xl flex flex-col gap-2">
              <BoxReveal animation="clip-right" staggerIndex={2}>
                <div className="bg-blue-dark text-primary p-4">
                  <h6 className="text-blue-light mb-2 uppercase">Event Format</h6>
                  <h5 className="text-balance">Semi-Pro <span className="text-blue-light">3x3 Mins</span><br />
                    Pro <span className="text-blue-light">6+ Rounds</span></h5>
                </div>
              </BoxReveal>
              <BoxReveal animation="clip-right" staggerIndex={3}>
                <div className="bg-blue-dark text-primary p-4">
                  <h6 className="text-blue-light mb-2 uppercase">Equipment</h6>
                  <h5>8-10 Ounce PRO lace-up gloves & hand wrapping</h5>
                </div>
              </BoxReveal>
              <BoxReveal animation="clip-right" staggerIndex={4}>
                <div className="bg-blue-dark text-primary p-4">
                  <h6 className="text-blue-light mb-2 uppercase">Scoring</h6>
                  <h5>Judged using the 10-point must system, based on effective aggression, defence, ring generalship, and clean punching</h5>
                </div>
              </BoxReveal>
              <BoxReveal animation="clip-right" staggerIndex={5}>
                <div className="bg-blue-dark text-primary p-4">
                  <h6 className="text-blue-light mb-2 uppercase">Knockdowns</h6>
                  <h5>mandatory 8-count, standing 8-counts & 3-knockdown rule may apply</h5>
                </div>
              </BoxReveal>
              {/* <div className="w-full min-w-fit p-6 relative border-blue-dark border flex flex-col gap-6">
              <h5 className="text-blue-dark justify-start uppercase">Event Fight card</h5>
              <EventTable />
            </div> */}
            </div>
          </div>
          <div className="w-full flex">
          <Link
            href="/Semi-Pro"
            className={`px-4 py-3 border-black border-[1px] mx-auto hover:bg-primary hover:text-secondary text-primary leading-tight bg-secondary flex justify-center items-center transition-all duration-300`}
          >
            <h5>
              More Info
            </h5>
          </Link></div>
        </VisibilityProvider>
      </section>

      <section className="h-[50vh] w-screen flex items-center justify-center relative overflow-hidden text-white">
        <div className="absolute inset-0 z-[-1] overflow-hidden">
          <div
            className="absolute top-1/4 -left-1/4 w-3/4 h-3/4 rounded-full bg-red-500 blur-[150px]"
            style={{ animation: 'pulse 8s ease-in-out infinite alternate' }}
          />
          <div
            className="absolute top-1/4 -right-1/4 w-3/4 h-3/4 rounded-full bg-blue-500 blur-[150px]"
            style={{ animation: 'pulse 8s ease-in-out infinite alternate-reverse' }}
          />
        </div>
        <div className="h-full w-full flex flex-col justify-center items-center z-10 px-4">
          <div className={`transition-opacity duration-500 ease-out sm:w-1/2 max-w-4xl h-1/2 bottom-0 flex flex-col justify-center items-center z-10 gap-6`}>
            <CountdownTimer launchDate={launchDate} />

            <h4 className="text-center text-white mb-6 leading-6">Due Drop Events Centre Manakau<br />June 29th</h4>
            <Link
              href="https://www.eventfinda.co.nz/2025/bx-9-new-zealand-boxing-series/auckland/manukau-city" target="_blank"
              className={`px-4 py-3 hover:bg-primary hover:text-secondary text-primary leading-tight bg-secondary flex justify-center items-center transition-all duration-300`}
            >
              <h5>
                Get Tickets
              </h5>
            </Link>
          </div>
        </div>
      </section>

      {register &&
        <section id="register" className="w-full bg-primary text-secondary px-6 md:px-12 py-20 flex flex-row justify-center gap-12 text-wrap flex-wrap">
          <div className="grow min-h-fit flex gap-6 flex-col justify-between relative">
            <h1 className="uppercase text-wrap">REGISTER<br />FOR BX-9</h1>
            <div className="relative aspect-video w-full">
              <Image src="/img/piks2.jpg" alt="Pikiao Tairua-Bracken Pro Kickboxer & Boxer" fill className="aspect-video object-cover object-top" />
              <p className="text-large hidden sm:block z-20 absolute bottom-0 left-0 p-6 text-primary uppercase">Pikiao Tairua-Bracken<br />Pro Kickboxer & Boxer</p>
            </div>
          </div>
          <div className="grow w-full lg:w-1/2">
            <h5 className="uppercase font-primary w-full">Step in. Stand out. Get paid.</h5>
            <p className="text-large w-full">Elite amateur or corporate boxer chasing a new challenge?</p>
            <p className="text-large w-full">Crossover athlete from kickboxing, MMA, rugby, or crossfit - ready for your next big test? Sign up to see if you have what it takes.</p>
            <br className="py-6" />
            <SignupForm />
          </div>
        </section>}
      <footer className="bg-secondary text-primary p-6 md:p-12 flex flex-col gap-6 md:gap-12 text-large">

        <div className="flex justify-between">

          <div className="w-32 md:w-48 h-10 md:h-14 relative bg-primary">
            <Image
              src="/img/BX-9.svg"
              alt="BX-9 Logo"
              fill
              className="object-contain"
            />
          </div>
          <div className="flex justify-center flex-col items-center">
            <h6>Powered By</h6>
            <div className="w-30 md:w-48 h-10 md:h-14 relative">
              <Logo showF={true} boxColor="transparent" />
            </div>
          </div>
        </div>
        <div className="w-full">
          <h1 className="font-bold uppercase text-red-light">a new breed</h1>
          <h1 className="font-bold uppercasetext-primary">of boxing is here</h1>
        </div>
        <div className="flex justify-between text-nowrap flex-wrap flex-row gap-6">
          <div className="flex flex-col md:flex-row gap-6 flex-wrap">
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary/20 gap-4">
              <p className="text-primary/50 uppercase text-left">Sanctioned by PBC NZ</p>
              <Image
                src="/img/pbc.png"
                alt="PBC NZ Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary/20 gap-4">
              <p className="text-primary/50 uppercase text-left">Live streamed globally on</p>
              <Image
                src="/img/csn.png"
                alt="CSN Streaming Platform Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 border-b border-r-0 md:border-r md:border-b-0 border-primary gap-4">
              <p className="text-primary/50 uppercase text-left">Recorded on BoxRec<br />as semi-pro (3 rounds)<br />and pro (4+ rounds).</p>
              <Image
                src="/img/boxrec.png"
                alt="Boxrec Logo"
                height={100}
                width={100}
                className="object-contain object-bottom"
              />
            </div>
            <div className="flex flex-col justify-between pr-6 pb-6 gap-4">
              <p className="text-primary/50 uppercase text-left">Need Help</p>
              <Link href="mailto:<EMAIL>" className="text-primary uppercase text-left"><EMAIL></Link>
            </div>
          </div>
          <Link href="https://www.duedropeventscentre.org.nz/" target="_blank"><Image
            src="/img/dewdrop.png"
            alt="Brand Logo"
            height={100}
            width={100}
            className="h-full object-contain object-bottom-right"
          /></Link>
        </div>
      </footer>
    </main>
  </>
  );
}
