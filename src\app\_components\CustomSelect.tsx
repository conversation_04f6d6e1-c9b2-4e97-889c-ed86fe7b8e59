'use client';

import { useState, useRef, useEffect } from 'react';
import { FaPlus } from 'react-icons/fa6';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  options: Option[];
  placeholder: string;
  name: string;
  value?: string;
  onChange?: (value: string) => void;
}

export default function CustomSelect({ options, placeholder, name, value, onChange }: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<Option | null>(
    options.find(opt => opt.value === value) ?? null
  );
  const selectRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(()=>{
    setSelectedOption(options.find(opt => opt.value === value) ?? null)
  },[options, value])

  const handleSelect = (option: Option) => {
    setSelectedOption(option);
    setIsOpen(false);
    onChange?.(option.value);
  };

  return (
    <div ref={selectRef} className="relative w-full">
      <div
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full bg-transparent cursor-pointer
          border-b border-secondary
          flex items-center justify-between
          py-2
        `}
      >
        <span className={`font-secondary text-base ${!selectedOption ? 'text-secondary/50' : 'text-secondary'}`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <FaPlus className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-45' : 'rotate-0'}`} />
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full bg-primary border border-secondary">
          {options.map((option) => (
            <div
              key={option.value}
              onClick={() => handleSelect(option)}
              className={`
                px-4 py-2 cursor-pointer font-secondary text-base
                ${selectedOption?.value === option.value ? 'bg-secondary/10 text-secondary' : 'text-secondary/50'}
                hover:bg-secondary/20 transition-colors
              `}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}

      {/* Hidden native select for form submission */}
      <select 
        name={name}
        value={selectedOption?.value ?? ''}
        onChange={(e) => {
          const option = options.find(opt => opt.value === e.target.value);
          if (option) handleSelect(option);
        }}
        className="hidden"
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}
