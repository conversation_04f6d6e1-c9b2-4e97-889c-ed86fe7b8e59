/* Base styles for animation wrappers */
[data-animate] {
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Hidden state (before in view) */
[data-animate="hidden"] {
  opacity: 0;
  transform: translateY(30px);
}

/* Visible state (after in view) */
[data-animate="visible"] {
  opacity: 1;
  transform: translateY(0);
}

/* Animation variations that can be applied with additional classes */
.fade-left[data-animate="hidden"] {
  transform: translateX(-30px);
}

.fade-right[data-animate="hidden"] {
  transform: translateX(30px);
}

.fade-up[data-animate="hidden"] {
  transform: translateY(30px);
}

.fade-down[data-animate="hidden"] {
  transform: translateY(-30px);
}

.scale[data-animate="hidden"] {
  transform: scale(0.9);
}

.rotate[data-animate="hidden"] {
  transform: rotate(-5deg);
}

/* Reset transforms for visible state */
.fade-left[data-animate="visible"],
.fade-right[data-animate="visible"],
.fade-up[data-animate="visible"],
.fade-down[data-animate="visible"],
.scale[data-animate="visible"],
.rotate[data-animate="visible"] {
  transform: translate(0) scale(1) rotate(0);
}

/* Animation delays for staggered animations */
.delay-100 {
  transition-delay: 100ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-400 {
  transition-delay: 400ms;
}

.delay-500 {
  transition-delay: 500ms;
} 