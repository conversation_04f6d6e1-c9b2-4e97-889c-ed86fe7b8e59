import "~/styles/globals.css";

import { type Metadata, type Viewport } from "next";
import localFont from "next/font/local";

import { TRPCReactProvider } from "~/trpc/react";
import { PostHogProvider } from "./providers/PostHogProvider";
import SiteIntro from "./_components/SiteIntro";

export const metadata: Metadata = {
  metadataBase: new URL("https://bx-9.com"),
  title: "BX-9",
  description: "A new breed of boxing is here. 29 June 2025",
  icons: [
    { rel: "icon", url: "/favicon.ico" },
    { rel: "apple-touch-icon", url: "/web-app-manifest-192x192.png" },
    { rel: "mask-icon", url: "/web-app-manifest-512x512.png" }
  ],
  manifest: "/manifest.json",
  openGraph: {
    title: "BX-9 Series",
    description: "A new breed of boxing is here. 29 June 2025",
    url: "/",
    type: "website",
    siteName: "BX-9",
    locale: "en-US",
  },
  twitter: {
    card: "summary_large_image",
    site: "@bx9series",
    creator: "@bx9series",
  },
  keywords: ["boxing", "BX-9", "sports", "fitness", "training"],
  authors: [{ name: "BX-9 Team" }],
  category: "Sports & Fitness",
  robots: "index, follow",
};

export const viewport: Viewport = {
  themeColor: 'black',
}

// Load PP Neue Corp fonts
const ppNeueMedium = localFont({
  src: '../../public/fonts/PPNeueCorp-NormalMedium.otf',
  variable: '--font-neuecorp-medium',
  display: 'swap',
});

const ppNeueUltrabold = localFont({
  src: '../../public/fonts/PPNeueCorp-ExtendedUltrabold.otf',
  variable: '--font-neuecorp-ultrabold',
  display: 'swap',
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${ppNeueMedium.variable} ${ppNeueUltrabold.variable} dark`}>
      <head>
        <meta property="og:logo" content="/favicon.ico" />
      </head>
      <body className="bg-black min-h-screen font-primary">
        <SiteIntro/>
        <PostHogProvider>
          <TRPCReactProvider>{children}</TRPCReactProvider>
        </PostHogProvider>
      </body>
    </html>
  );
}
