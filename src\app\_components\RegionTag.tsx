"use client";
import { useEffect, useState, useRef } from "react";

interface RegionTagProps {
  name: string;
  position: {
    top: string;
    left?: string;
    right?: string;
  };
  direction?: "left" | "right";
  tail: {
    height: number;
    width: number;
  }
  color: "red" | "orange" | "blue" | "green" | "turquoise" | "baby-blue" | "purple" | "pink" | "yellow" | "white" | "black";
}

export default function RegionTag({ name, position, tail, color, direction = "left" }: RegionTagProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry?.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 1,
        rootMargin: '-25% 0px -25% 0px' // This creates a smaller trigger area in the middle 50% of the viewport
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  const formattedName = name.split('\\n').map((line, i) => (
    <span key={i} className="block w-full">{line}</span>
  ));
  let textColor = "text-white";
  let boxColor = "bg-black";
  switch (color) {
    case "red":
      textColor = "text-red-dark";
      boxColor = "bg-red-light";
      break; case "orange":
      textColor = "text-orange-dark";
      boxColor = "bg-orange-light";
      break; case "blue":
      textColor = "text-blue-dark";
      boxColor = "bg-blue-light";
      break; case "green":
      textColor = "text-green-dark";
      boxColor = "bg-green-light";
      break; case "turquoise":
      textColor = "text-turquoise-dark";
      boxColor = "bg-turquoise-light";
      break; case "baby-blue":
      textColor = "text-baby-blue-dark";
      boxColor = "bg-baby-blue-light";
      break; case "purple":
      textColor = "text-purple-dark";
      boxColor = "bg-purple-light";
      break; case "pink":
      textColor = "text-pink-dark";
      boxColor = "bg-pink-light";
      break; case "yellow":
      textColor = "text-yellow-dark";
      boxColor = "bg-yellow-light";
      break; case "white":
      textColor = "text-white";
      boxColor = "bg-white";
      break; case "black":
      textColor = "text-black";
      boxColor = "bg-black";
      break;
  
    default:
      break;
  }

  const [tailWidth, setTailWidth] = useState(0);

  useEffect(() => {
    const updateWidth = () => {
      if (ref.current) {
        const parentElement = ref.current.parentElement;
        if (parentElement) {
          setTailWidth(parentElement.offsetWidth*(tail.width/100));
        }
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);

    return () => window.removeEventListener('resize', updateWidth);
  }, [tail.width]);

  return (
    <div className="absolute" style={position} ref={ref}>
      {/* <div className="absolute top-0 left-0 w-1 h-1 bg-red-light rounded-full"></div> */}
      <div 
        // className={`${direction === "left" ? "border-l" : "-translate-x-full border-r"} translate-y-[calc(100%_+_10px)] absolute top-0 left-0 border-black border-b transition-[clip-path] duration-1000 ease-in-out`}
        className={`-translate-y-full ${direction === "right" ? "border-r" : "-translate-x-full border-l"} absolute top-0 left-0 border-black border-b transition-[clip-path] duration-1000 ease-in-out`}
        style={{
          height: tail.height+"px",
          width: tailWidth+"px",
          clipPath: isVisible ? 'inset(0 0 0 0)' : direction === "left" ? 'inset(0 0 0 100%)' : 'inset(0 100% 0 0)'
        }}
      />
      <div 
        // className={`absolute top-0 left-0 translate-x-[-50%] translate-y-[-100%] bg-${color}-light transition-transform duration-300 ease-out`}
        className={`absolute top-0 left-0 ${boxColor} transition-transform duration-300 ease-out scale-70 sm:scale-100`}
        style={{
          transform: `scale(${isVisible ? 1 : 0})`,
          translate: direction === "right" ? `calc(-50% + ${tailWidth}px) calc(-110% - ${tail.height}px)` : `calc(-50% - ${tailWidth}px) calc(-110% - ${tail.height}px)`,
          transitionDelay: '0.8s'
        }}
      >
        <h6 dir={direction === "right" ? "rtl" : "ltr"} className={`whitespace-nowrap uppercase px-2 py-1 ${textColor}`}>
          {formattedName}
        </h6>
      </div>
    </div>
  );
}






