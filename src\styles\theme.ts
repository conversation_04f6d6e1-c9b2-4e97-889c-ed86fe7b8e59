export const theme = {
  colors: {
    // Primary Colors
    white: '#FFFFFF',
    black: '#000000',

    // Secondary Colors
    red: {
      light: '#ED1A27',
      dark: '#470707'
    },
    blue: {
      light: '#3355FF',
      dark: '#0F163F'
    },

    // Tertiary Colors
    orange: {
      light: '#FF7936',
      dark: '#612C05'
    },
    yellow: {
      light: '#FFED2C',
      dark: '#3C3A00'
    },
    green: {
      light: '#52E000',
      dark: '#204211'
    },
    turquoise: {
      light: '#33FF7C',
      dark: '#0D3F13'
    },
    babyBlue: {
      light: '#33D9FF',
      dark: '#043644'
    },
    purple: {
      light: '#8233FF',
      dark: '#251540'
    },
    pink: {
      light: '#FF647D',
      dark: '#4E1122'
    }
  },

  // Add these CSS variables to your global styles
  cssVariables: `
    :root {
      /* Primary Colors */
      --color-white: #FFFFFF;
      --color-black: #000000;

      /* Secondary Colors */
      --color-red-light: #ED1A27;
      --color-red-dark: #470707;
      --color-blue-light: #3355FF;
      --color-blue-dark: #0F163F;

      /* Tertiary Colors */
      --color-orange-light: #FF7936;
      --color-orange-dark: #612C05;
      --color-yellow-light: #FFED2C;
      --color-yellow-dark: #3C3A00;
      --color-green-light: #52E000;
      --color-green-dark: #204211;
      --color-turquoise-light: #33FF7C;
      --color-turquoise-dark: #0D3F13;
      --color-baby-blue-light: #33D9FF;
      --color-baby-blue-dark: #043644;
      --color-purple-light: #8233FF;
      --color-purple-dark: #251540;
      --color-pink-light: #FF647D;
      --color-pink-dark: #4E1122;
    }

    .dark {
      /* You can add dark mode specific overrides here */
    }
  `,

  // Common gradients
  gradients: {
    redToBlue: 'linear-gradient(to right, var(--color-red-light), var(--color-blue-light))',
    purpleToPink: 'linear-gradient(to right, var(--color-purple-light), var(--color-pink-light))',
    greenToTurquoise: 'linear-gradient(to right, var(--color-green-light), var(--color-turquoise-light))',
    orangeToYellow: 'linear-gradient(to right, var(--color-orange-light), var(--color-yellow-light))',
  },

  // Spacing scale
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '2.5rem',  // 40px
    '3xl': '3rem',    // 48px
  },

  // Border radius
  borderRadius: {
    sm: '0.25rem',    // 4px
    md: '0.5rem',     // 8px
    lg: '1rem',       // 16px
    full: '9999px',
  },

  // Transitions
  transitions: {
    fast: '0.15s ease',
    normal: '0.3s ease',
    slow: '0.5s cubic-bezier(0.16, 1, 0.3, 1)',
  },

  // Z-index scale
  zIndex: {
    base: 0,
    above: 1,
    below: -1,
    nav: 100,
    modal: 200,
    overlay: 300,
    max: 999,
  },
} as const;

// Type definitions
export type Theme = typeof theme;
export type ThemeColors = typeof theme.colors;
type NestedColor = string | { [key: string]: NestedColor };

// Utility function to get nested color values with proper typing
export const getColor = (path: string): string | undefined => {
  const parts = path.split('.');
  let result: NestedColor = theme.colors;
  
  for (const part of parts) {
    if (typeof result !== 'object' || result === null || !(part in result)) {
      console.warn(`Color path "${path}" not found in theme`);
      return undefined;
    }
    result = result[part]!;
    if (result === undefined) {
      return undefined;
    }
  }
  
  return typeof result === 'string' ? result : undefined;
}; 