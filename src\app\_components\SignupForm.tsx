'use client'

import { useEffect, useState } from 'react';
import { api } from "~/trpc/react";
import CustomSelect from "./CustomSelect";
import { MediaUpload } from "./MediaUpload";
import type { SignupFormData } from "~/server/api/schemas/signup";
interface FieldError {
  message: string;
}

type FormErrors = Record<string, FieldError[]>;

export default function SignupForm() {
  const [formData, setFormData] = useState<Partial<SignupFormData>>({});
  const [files, setFiles] = useState<File[]>([]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStatus, setFormStatus] = useState<{
    type: 'idle' | 'success' | 'error';
    message: string;
  }>({ type: 'idle', message: '' });
  const [fieldErrors, setFieldErrors] = useState<FormErrors>({});

  const signupMutation = api.signup.submit.useMutation({
    onSuccess: (data) => {
      setFormStatus({ 
        type: 'success', 
        message: data.message ?? 'Form submitted successfully!' 
      });
      const messageRef = document.querySelector(`#form-status`);
      if (messageRef) {
        messageRef.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center'
        });
      }
      setFieldErrors({});
      setIsSubmitting(false);
    },
    onError: (error) => {
      // Handle Zod validation errors
      if (error.data?.zodError) {
        const fieldErrors: FormErrors = {};
        if (error.data.zodError.fieldErrors) {
          Object.entries(error.data.zodError.fieldErrors).forEach(([field, errors]) => {
            if (errors) {
              fieldErrors[field] = errors.map(err => ({ message: err }));
            }
          });
        }
        setFieldErrors(fieldErrors);
        setFormStatus({ 
          type: 'error', 
          message: 'Please fill in all required fields.' 
        });
        if (Object.keys(fieldErrors).length > 0) {
          const firstErrorField = Object.keys(fieldErrors)[0];
          const errorElement = document.querySelector(`[name="${firstErrorField}"]`);
          if (errorElement) {
            errorElement.scrollIntoView({ 
              behavior: 'smooth', 
              block: 'center'
            });
          }
        }
      } else {
        setFormStatus({ 
          type: 'error', 
          message: error.message 
        });
      }
      setIsSubmitting(false);
    },
  });
  const presignedURLMutation = api.signup.getPresignedURL.useMutation({
    onSuccess: () => {
      console.log('Successfully got presigned URL');
    },
    onError: (error) => {
      console.error('Error getting presigned URL:', error);
    },
  });


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    // Clear error for this field
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    // Clear error for this field
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFilesChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFormStatus({ type: 'idle', message: '' });
    
    try {
      const mediaFiles: string[] = [];
      for (const file of files ?? []) {
        const { url, key, bucket } = await presignedURLMutation.mutateAsync({
          fileName: file.name,
          fileType: file.type
        });
        const req = await fetch(url, {
          method: 'PUT',
          body: file,
          headers: { 'Content-Type': file.type }
        });
        console.log(req)
        mediaFiles.push(`https://${bucket}.s3.amazonaws.com/${key}`);
        console.log(mediaFiles)
      }
      signupMutation.mutate({...formData, mediaFiles} as SignupFormData);
    } catch (error) {
      console.error('Form submission error:', error);
      setFormStatus({
        type: 'error',
        message: 'Failed to submit form. Please try again.',
      });
      setIsSubmitting(false);
    }
  };


  // Add dummy data for development environment
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setFormData({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        dateOfBirth: '1990-01-01',
        bio: 'I am a professional athlete with 5 years of experience.',
        instagram: '@johndoe',
        tiktok: '@johndoe',
        gymName: 'FitZone Gym',
        coachName: 'Mike Johnson',
        coachPhoneNumber: '0987654321',
        coachEmail: '<EMAIL>',
        region: 'auckland',
        weightClass: 'middleweight',
        fightRecords: '10 wins, 2 losses in professional boxing',
        joinReason: 'I want to join BX-9 to challenge myself and grow as an athlete.'
      });
    }
  }, []);

  return (
    <form onSubmit={handleSubmit} className="space-y-4  text-small">
      {formStatus.type !== 'success' && (<>
        <h6 className="uppercase font-primary w-full">Athlete Details</h6>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.firstName ? 'text-red-light' : ''}`}>First Name</label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName ?? ''}
              onChange={handleInputChange}
              placeholder="John"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.lastName ? 'text-red-light' : ''}`}>Last Name</label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName ?? ''}
              onChange={handleInputChange}
              placeholder="Doe"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.email ? 'text-red-light' : ''}`}>Email</label>
            <input
              type="email"
              name="email"
              value={formData.email ?? ''}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className={`bg-transparent mt-1 w-full outline-none ${fieldErrors.email ? 'border-red-500' : ''}`}
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.phoneNumber ? 'text-red-light' : ''}`}>Phone Number</label>
            <input
              type="tel"
              name="phoneNumber"
              value={formData.phoneNumber ?? ''}
              onChange={handleInputChange}
              placeholder="0211234567"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.dateOfBirth ? 'text-red-light' : ''}`}>Date of Birth (18+)</label>
            <input
              type="date"
              name="dateOfBirth"
              value={formData.dateOfBirth ?? ''}
              onChange={handleInputChange}
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.bio ? 'text-red-light' : ''}`}>Bio</label>
            <textarea
              name="bio"
              value={formData.bio ?? ''}
              onChange={handleInputChange}
              placeholder="Write a short bio about yourself..."
              className="bg-transparent mt-1 w-full h-40 outline-none resize-none"
            />
          </div>
        </div>
        <h6 className="uppercase font-primary w-full mt-4">Socials</h6>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.instagram ? 'text-red-light' : ''}`}>Instagram</label>
            <input
              type="text"
              name="instagram"
              value={formData.instagram ?? ''}
              onChange={handleInputChange}
              placeholder="@johndoe"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.tiktok ? 'text-red-light' : ''}`}>TikTok</label>
            <input
              type="text"
              name="tiktok"
              value={formData.tiktok ?? ''}
              onChange={handleInputChange}
              placeholder="@johndoe"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
        </div>
        <h6 className="uppercase font-primary w-full mt-4">Coach/Gym Details</h6>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.gymName ? 'text-red-light' : ''}`}>Gym Name</label>
            <input
              type="text"
              name="gymName"
              value={formData.gymName ?? ''}
              onChange={handleInputChange}
              placeholder="FitZone Gym"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.coachName ? 'text-red-light' : ''}`}>Coach Name</label>
            <input
              type="text"
              name="coachName"
              value={formData.coachName ?? ''}
              onChange={handleInputChange}
              placeholder="Jane Doe"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.coachPhoneNumber ? 'text-red-light' : ''}`}>Coach Phone Number</label>
            <input
              type="tel"
              name="coachPhoneNumber"
              value={formData.coachPhoneNumber ?? ''}
              onChange={handleInputChange}
              placeholder="021234567"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.coachEmail ? 'text-red-light' : ''}`}>Coach Email</label>
            <input
              type="email"
              name="coachEmail"
              value={formData.coachEmail ?? ''}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className="bg-transparent mt-1 w-full outline-none"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.region ? 'text-red-light' : ''}`}>Region</label>
            <CustomSelect
              name="region"
              value={formData.region}
              onChange={(value) => handleSelectChange('region', value)}
              placeholder="Select region you represent"
              options={[
                { value: 'auckland', label: 'Auckland' },
                { value: 'northland', label: 'Northland' },
                { value: 'centralNorthIsland', label: 'Central North Island' },
                { value: 'westNorthIsland', label: 'West North Island' },
                { value: 'eastNorthIsland', label: 'East North Island' },
                { value: 'wellington', label: 'Wellington' },
                { value: 'upperSouthIsland', label: 'Upper South Island' },
                { value: 'centralSouthIsland', label: 'Central South Island' },
                { value: 'lowerSouthIsland', label: 'Lower South Island' },
              ]}
            />
          </div>
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.weightClass ? 'text-red-light' : ''}`}>Weight Class</label>
            <CustomSelect
              name="weightClass"
              value={formData.weightClass}
              onChange={(value) => handleSelectChange('weightClass', value)}
              placeholder="Select your weight class"
              options={[
                { value: 'flyweight', label: 'Flyweight (57KG)' },
                { value: 'bantamweight', label: 'Bantamweight (62KG)' },
                { value: 'featherweight', label: 'Featherweight (66KG)' },
                { value: 'lightweight', label: 'Lightweight (70KG)' },
                { value: 'welterweight', label: 'Welterweight (76KG)' },
                { value: 'middleweight', label: 'Middleweight (80KG)' },
                { value: 'lightheavyweight', label: 'Light Heavyweight (86KG)' },
                { value: 'cruiserweight', label: 'Cruiserweight (95KG)' },
                { value: 'heavyweight', label: 'Heavyweight (100KG+)' },
              ]}
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <label className={`${fieldErrors.fightRecords ? 'text-red-light' : ''}`}>Fight Records</label>
            <textarea
              name="fightRecords"
              value={formData.fightRecords ?? ''}
              onChange={handleInputChange}
              placeholder="List all records including pro, amateur & corporate bouts across Boxing, Kickboxing, MMA etc..."
              className="bg-transparent mt-1 w-full h-40 outline-none resize-none"
            />
          </div>
        </div>
        <h6 className={`uppercase font-primary w-full mt-4 ${fieldErrors.joinReason ? 'text-red-light' : ''}`}>Why do you want to be considered to join BX-9</h6>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-b border-secondary">
            <textarea
              name="joinReason"
              value={formData.joinReason ?? ''}
              onChange={handleInputChange}
              placeholder="Tell us why you want to join BX-9"
              className="bg-transparent mt-1 w-full h-40 outline-none resize-none"
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2">
          <div className="w-full border-secondary">
            <label>Media Upload</label>
            <MediaUpload onFilesChange={handleFilesChange} />
          </div>
        </div>
      </>)}
      
      {/* Status message */}
      {(Object.keys(fieldErrors).length > 0 || formStatus.type === 'success') && (
        <div id="form-status" className={`p-2 text-xs font-primary ${
          formStatus.type === 'success' ? 'text-green-dark bg-green-light' : 
          formStatus.type === 'error' ? 'text-red-dark bg-red-light' : ''
        }`}>
          {formStatus.message}
        </div>
      )}
      
      {formStatus.type !== 'success' && (
        <button
          type="submit"
          disabled={isSubmitting || Object.keys(fieldErrors).length > 0}
          className={`w-full bg-secondary text-primary py-2 ${
            isSubmitting || Object.keys(fieldErrors).length > 0 ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting ? 'Registering...' : 'Register'}
        </button>
      )}
    </form>
  );
}







