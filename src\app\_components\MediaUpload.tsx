'use client'
import { useState, useCallback } from 'react';
import Image from 'next/image';

interface MediaUploadProps {
  onFilesChange?: (files: File[]) => void;
}

export const MediaUpload = ({ onFilesChange }: MediaUploadProps) => {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const handleFiles = useCallback((newFiles: FileList | null) => {
    if (!newFiles) return;

    const filesArray = Array.from(newFiles);

    setFiles(prevFiles => {
      const combined = [...prevFiles, ...filesArray];
      // Move the onFilesChange callback outside of setState
      setTimeout(() => {
        onFilesChange?.(combined);
      }, 0);
      return combined;
    });
  }, [onFilesChange]);

  const removeFile = useCallback((indexToRemove: number) => {
    setFiles(prev => {
      const newFiles = prev.filter((_, index) => index !== indexToRemove);
      // Move the onFilesChange callback outside of setState
      setTimeout(() => {
        onFilesChange?.(newFiles);
      }, 0);
      return newFiles;
    });
  }, [onFilesChange]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setIsDragging(true);
    } else if (e.type === 'dragleave' || e.type === 'drop') {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  }, [handleFiles]);

  return (
    <div className={`w-full border mt-2 border-secondary/50 border-dashed ${isDragging ? 'bg-secondary/10' : ''}`}>
      <div 
        className={`relative w-full h-40 transition-colors duration-200`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        <input 
          type="file" 
          name="mediaUpload" 
          accept="image/*,video/*"
          multiple
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10" 
          onChange={(e) => handleFiles(e.target.files)}
        />
        <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 pointer-events-none">
          <svg className="w-6 h-6 text-secondary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 4V20M4 12H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <p className="text-secondary/50 text-sm font-secondary text-center">
            Add a short video (1–2 minutes) introducing yourself, your boxing experience, and why you want to register. This helps us get to know you beyond the form. Make it real - no need to be fancy.
          </p>
        </div>
      </div>

      {files.length > 0 && (
        <div className="grid grid-cols-4 gap-4 m-2 border-t border-secondary/50">
          {files.map((file, index) => (
            <div key={index} className="relative aspect-square border border-secondary bg-primary">
              {file.type.startsWith('image/') ? (
                <Image
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <video
                  src={URL.createObjectURL(file)}
                  className="w-full h-full object-cover"
                />
              )}
              <button
                onClick={(e) => {e.preventDefault();removeFile(index)}}
                className="absolute top-2 right-2 w-6 h-6 bg-secondary/50 hover:bg-red-light/75 rounded-full flex items-center justify-center"
              >
                <svg className="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};



