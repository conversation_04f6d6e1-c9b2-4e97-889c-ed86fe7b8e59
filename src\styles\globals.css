@import "tailwindcss";

@theme {
  /* Primary Colors */
  --color-primary: #FFFFFF;
  --color-secondary: #000000;

  --color-white: #FFFFFF;
  --color-black: #000000;

  /* Secondary Colors */
  --color-red-light: #ED1A27;
  --color-red-dark: #470707;
  --color-blue-light: #3355FF;
  --color-blue-dark: #0F163F;

  /* Tertiary Colors */
  --color-orange-light: #FF7936;
  --color-orange-dark: #612C05;
  --color-yellow-light: #FFED2C;
  --color-yellow-dark: #3C3A00;
  --color-green-light: #52E000;
  --color-green-dark: #204211;
  --color-turquoise-light: #33FF7C;
  --color-turquoise-dark: #0D3F13;
  --color-baby-blue-light: #33D9FF;
  --color-baby-blue-dark: #043644;
  --color-purple-light: #8233FF;
  --color-purple-dark: #251540;
  --color-pink-light: #FF647D;
  --color-pink-dark: #4E1122;
  --color-grey-light: #171717;


  /* Fonts */
  --font-sans: var(--font-neuecorp-medium), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  /* Screen Sizes */
  --screen-2xl: 1536px;
}

/* Desktop Sizes */
:root {
  /* Desktop */
  --border-width-main: 0.0625rem;
  --column-width-1: 6.0625rem;
  --column-width-2: 13.1875rem;
  --column-width-3: 20.25rem;
  --column-width-4: 27.3125rem;
  --column-width-5: 34.4375rem;
  --column-width-6: 41.5rem;
  --column-width-7: 48.5625rem;
  --column-width-8: 55.6875rem;
  --column-width-9: 62.75rem;
  --column-width-10: 69.8125rem;
  --column-width-11: 76.9375rem;
  --column-width-12: 84rem;
  --grid-gap-main: var(--size-1);
  --padding-horizontal-main: var(--size-3);
  --padding-vertical-none: var(--size-0);
  --padding-vertical-small: var(--size-5);
  --padding-vertical-main: var(--size-7);
  --padding-vertical-large: var(--size-10);
  --radius-small: 0.5rem;
  --radius-main: 1rem;
  --radius-round: 6249.9375rem;
  --size-0: 0rem;
  --size-0_125: 0.125rem;
  --size-0_25: 0.25rem;
  --size-0_5: 0.5rem;
  --size-0_75: 0.75rem;
  --size-1: 1rem;
  --size-1_25: 1.25rem;
  --size-1_5: 1.5rem;
  --size-1_75: 1rem;
  --size-2: 2rem;
  --size-2_5: 2.5rem;
  --size-3: 3rem;
  --size-3_5: 3.5rem;
  --size-4: 4rem;
  --size-4_5: 4.5rem;
  --size-5: 5rem;
  --size-5_5: 5.5rem;
  --size-6: 6rem;
  --size-6_5: 6.5rem;
  --size-7: 7rem;
  --size-7_5: 7.5rem;
  --size-8: 8rem;
  --size-8_5: 8.5rem;
  --size-9: 9rem;
  --size-9_5: 9.5rem;
  --size-10: 10rem;
  --size-11: 11rem;
  --size-12: 12rem;
  --size-13: 13rem;
  --size-14: 14rem;
  --size-15: 15rem;
  --size-16: 16rem;
  --space-none: var(--size-0);
  --space-extra-small: var(--size-0_75);
  --space-small: var(--size-1_5);
  --space-medium: var(--size-3);
  --space-large: var(--size-4);
}

/* Mobile Sizes */
@media (max-width: 1023px) {
  :root {
    --border-width-main: 0.0938rem;
    --column-width-1: 6.0625rem;
    --column-width-2: 13.1875rem;
    --column-width-3: 20.25rem;
    --column-width-4: 27.3125rem;
    --column-width-5: 34.4375rem;
    --column-width-6: 41.5rem;
    --column-width-7: 48.5625rem;
    --column-width-8: 55.6875rem;
    --column-width-9: 62.75rem;
    --column-width-10: 69.8125rem;
    --column-width-11: 76.9375rem;
    --column-width-12: 84rem;
    --grid-gap-main: var(--size-1);
    --padding-horizontal-main: var(--size-1);
    --padding-vertical-none: var(--size-0);
    --padding-vertical-small: var(--size-5);
    --padding-vertical-main: var(--size-7);
    --padding-vertical-large: var(--size-10);
    --radius-small: 0.5rem;
    --radius-main: 1rem;
    --radius-round: 6249.9375rem;
    --size-0: 0rem;
    --size-0_125: 0.125rem;
    --size-0_25: 0.25rem;
    --size-0_5: 0.5rem;
    --size-0_75: 0.75rem;
    --size-1: 1rem;
    --size-1_25: 1.25rem;
    --size-1_5: 1.2rem;
    --size-1_75: 1rem;
    --size-2: 1.75rem;
    --size-2_5: 1.3rem;
    --size-3: 1.5rem;
    --size-3_5: 2.375rem;
    --size-4: 2rem;
    --size-4_5: 2.75rem;
    --size-5: 2.4rem;
    --size-5_5: 3.25rem;
    --size-6: 3.5rem;
    --size-6_5: 3.75rem;
    --size-7: 4rem;
    --size-7_5: 4.25rem;
    --size-8: 4.5rem;
    --size-8_5: 4.75rem;
    --size-9: 5rem;
    --size-9_5: 5.25rem;
    --size-10: 5.5rem;
    --size-11: 5.75rem;
    --size-12: 6rem;
    --size-13: 6.5rem;
    --size-14: 7rem;
    --size-15: 7.5rem;
    --size-16: 8rem;
    --space-none: var(--size-0);
    --space-extra-small: var(--size-0_75);
    --space-small: var(--size-1_5);
    --space-medium: var(--size-3);
    --space-large: var(--size-4);
  }
}

/* Small Mobile sizes <500px width */
@media (max-width: 500px) {
  :root {
    --size-0: 0rem;
    --size-0_125: 0.125rem;
    --size-0_25: 0.25rem;
    --size-0_5: 0.5rem;
    --size-0_75: 0.75rem;
    --size-1: 0.7rem;
    --size-1_25: 1.25rem;
    --size-1_5: 1.2rem;
    --size-1_75: 1rem;
    --size-2: 1.5rem;
    --size-2_5: 1.1rem;
    --size-3: 1.3rem;
    --size-3_5: 2rem;
    --size-4: 1.75rem;
    --size-4_5: 2.25rem;
    --size-5: 2rem;
    --size-5_5: 2.75rem;
    --size-6: 3rem;
    --size-6_5: 3.25rem;
    --size-7: 3.5rem;
    --size-7_5: 3.75rem;
    --size-8: 4rem;
    --size-8_5: 4.25rem;
    --size-9: 4.5rem;
    --size-9_5: 4.75rem;
    --size-10: 5rem;
    --size-11: 5.25rem;
    --size-12: 5.5rem;
    --size-13: 6rem;
    --size-14: 6.5rem;
    --size-15: 7rem;
    --size-16: 7.5rem;
  }
}

:root {
  /* Typography */
  /* number */
  --display-letter-spacing: 0;
  --display-line-height: var(--size-5_5);
  --display-font-size: var(--size-7);
  --h1-letter-spacing: 0;
  --h1-line-height: var(--size-4);
  --h1-font-size: var(--size-5);
  --h2-letter-spacing: 0;
  --h2-line-height: var(--size-3);
  --h2-font-size: var(--size-4);
  --h3-letter-spacing: 0;
  --h3-line-height: var(--size-2_5);
  --h3-font-size: var(--size-3);
  --h4-letter-spacing: 0;
  --h4-line-height: var(--size-1_5);
  --h4-font-size: var(--size-2);
  --h5-letter-spacing: 0;
  --h5-line-height: var(--size-1_25);
  --h5-font-size: var(--size-1_5);
  --h6-letter-spacing: 0;
  --h6-line-height: var(--size-1_75);
  --h6-font-size: var(--size-1);
  --text-large-letter-spacing: 0;
  --text-large-font-size: var(--size-1_25);
  --text-large-line-height: var(--size-1_5);
  --text-main-letter-spacing: 0;
  --text-main-font-size: var(--size-1);
  --text-main-line-height: var(--size-1_25);
  --text-small-letter-spacing: 0;
  --text-small-font-size: var(--size-1);
  --text-small-line-height: var(--size-1_25);
  
  /* string */
  --display-font-family: var(--font-family-primary-family);
  --display-font-weight: var(--font-weight-primary-bold);
  --font-family-primary-family: PP Neue Corp;
  --font-weight-primary-bold: Extended Ultrabold;
  --font-weight-primary-medium: Normal Medium;
  --font-weight-primary-regular: Tight Medium;
  --h1-font-family: var(--font-family-primary-family);
  --h1-font-weight: var(--font-weight-primary-bold);
  --h2-font-family: var(--font-family-primary-family);
  --h2-font-weight: var(--font-weight-primary-bold);
  --h3-font-family: var(--font-family-primary-family);
  --h3-font-weight: var(--font-weight-primary-bold);
  --h4-font-family: var(--font-family-primary-family);
  --h4-font-weight: var(--font-weight-primary-bold);
  --h5-font-family: var(--font-family-primary-family);
  --h5-font-weight: var(--font-weight-primary-bold);
  --h6-font-family: var(--font-family-primary-family);
  --h6-font-weight: var(--font-weight-primary-bold);
  --text-large-font-family: var(--font-family-primary-family);
  --text-large-font-weight: var(--font-weight-primary-medium);
  --text-main-font-family: var(--font-family-primary-family);
  --text-main-font-weight: var(--font-weight-primary-medium);
  --text-small-font-family: var(--font-family-primary-family);
  --text-small-font-weight: var(--font-weight-primary-medium);
}

@layer base {

  title, .title {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--display-font-size, 112px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--display-line-height, 88px);
    letter-spacing: var(--display-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h1, .h1 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h1-font-size, 80px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h1-line-height, 64px);
    letter-spacing: var(--h1-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h2, .h2 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h2-font-size, 64px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h2-line-height, 48px);
    letter-spacing: var(--h2-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h3, .h3 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h3-font-size, 48px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h3-line-height, 40px);
    letter-spacing: var(--h3-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h4, .h4 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h4-font-size, 32px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h4-line-height, 24px);
    letter-spacing: var(--h4-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h5, .h5 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h5-font-size, 24px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h5-line-height, 20px);
    letter-spacing: var(--h5-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  h6, .h6 {
    font-family: var(--font-neuecorp-ultrabold);
    font-size: var(--h6-font-size, 16px);
    font-style: normal;
    font-weight: 700;
    line-height: var(--h6-line-height, 12px);
    letter-spacing: var(--h6-letter-spacing, 0px);
    text-transform: uppercase;
  }
  
  .text-main {
    font-family: var(--font-neuecorp-medium);
    font-size: var(--text-main-font-size, 16px);
    font-style: normal;
    font-weight: 500;
    line-height: var(--text-main-line-height, 20px);
    letter-spacing: var(--text-main-letter-spacing, 0px);
  }
  
  .text-large {
    font-family: var(--font-neuecorp-medium);
    font-size: var(--text-large-font-size, 20px);
    font-style: normal;
    font-weight: 500;
    line-height: var(--text-large-line-height, 24px);
    letter-spacing: var(--text-large-letter-spacing, 0px);
  }
  
  .text-small {
    font-family: var(--font-neuecorp-medium);
    font-size: var(--text-small-font-size, 14px);
    font-style: normal;
    font-weight: 500;
    line-height: var(--text-small-line-height, 20px);
    letter-spacing: var(--text-small-letter-spacing, 0px);
  }
}

@layer base {
  :root {
    /* BX-9 Brand Colors */
    --primary: 255 0 255; /* Magenta */
    --secondary: 0 255 255; /* Cyan */
    --accent: 255 255 0; /* Yellow */
    --background: 0 0 0; /* Black */
    --foreground: 255 255 255; /* White */
  }
}

@layer utilities {
  .font-neuecorp-medium {
    font-family: var(--font-neuecorp-medium);
    font-weight: 500;
  }
  
  .font-neuecorp-ultrabold {
    font-family: var(--font-neuecorp-ultrabold);
    font-weight: 800;
  }
  
  /* Redirect existing utility classes to use the new classes */
  .font-secondary {
    font-family: var(--font-neuecorp-medium);
    font-weight: 500;
  }
  
  .font-primary {
    font-family: var(--font-neuecorp-ultrabold);
    font-weight: 800;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

