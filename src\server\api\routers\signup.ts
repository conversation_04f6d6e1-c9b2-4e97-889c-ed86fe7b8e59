import { TRPCError } from "@trpc/server";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import { signupSchema } from "../schemas/signup";
import { Prisma } from "@prisma/client";
import { z } from "zod";
import { env } from "~/env";
import { s3Client } from "~/utils/s3";

const getHumanReadableError = (error: unknown) => {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle known Prisma errors
    switch (error.code) {
      case 'P2002': {
        const field = (error.meta?.target as string[])?.[0] ?? 'field';
        return `An application with this ${field} already exists`;
      }
      case 'P2014':
        return 'The data provided is invalid';
      case 'P2003':
        return 'Invalid reference to related data';
      default:
        return 'There was an error processing your application';
    }
  }

  if (error instanceof Prisma.PrismaClientValidationError) {
    return 'The provided data is invalid';
  }

  if (error instanceof Error) {
    // Handle S3 and other known errors
    if (error.message.includes('S3') || error.message.includes('upload')) {
      return 'Failed to upload media files. Please try again';
    }
    
    // For development, log the actual error
    if (process.env.NODE_ENV === 'development') {
      console.error('Detailed error:', error);
    }
  }

  return 'An unexpected error occurred. Please try again later';
};

export const signupRouter = createTRPCRouter({
  submit: publicProcedure
    .input(signupSchema)
    .mutation(async ({ ctx, input }) => {
      try {

        // Create signup record with media URLs
        const signup = await ctx.db.signup.create({
          data: {
            firstName: input.firstName,
            lastName: input.lastName,
            email: input.email,
            phoneNumber: input.phoneNumber,
            dateOfBirth: new Date(input.dateOfBirth),
            bio: input.bio,
            instagram: input.instagram,
            tiktok: input.tiktok,
            gymName: input.gymName ?? '',
            coachName: input.coachName,
            coachPhoneNumber: input.coachPhoneNumber,
            coachEmail: input.coachEmail,
            region: input.region,
            weightClass: input.weightClass,
            fightRecords: input.fightRecords,
            joinReason: input.joinReason,
            mediaFiles: input.mediaFiles,
          },
        });

        return { 
          success: true, 
          signup,
          message: "Thanks for your submission! We'll be in touch soon."
        };
      } catch (error) {
        console.error("Signup error:", error);
        
        const errorMessage = getHumanReadableError(error);
        
        throw new TRPCError({
          code: error instanceof Prisma.PrismaClientKnownRequestError && 
                error.code === 'P2002' ? 'CONFLICT' : 'INTERNAL_SERVER_ERROR',
          message: errorMessage,
        });
      }
    }),
  
    getPresignedURL: publicProcedure
    .input(z.object({
      fileName: z.string(),
      fileType: z.string()
    }))
    .mutation(async ({ input }) => {
      try {
        const { getSignedUrl } = await import("@aws-sdk/s3-request-presigner");
        const { PutObjectCommand } = await import("@aws-sdk/client-s3");
        
        const key = `SignupMedia/${input.fileName}-${Date.now()}`;
        
        const command = new PutObjectCommand({
          Bucket: env.S3_BUCKET_NAME,
          Key: key,
          ContentType: input.fileType,
        });

        // Generate presigned URL valid for 5 minutes
        const url = await getSignedUrl(s3Client, command, { expiresIn: 300 });

        return {
          url,
          key,
          bucket: env.S3_BUCKET_NAME
        };
      } catch (error) {
        console.error("Presigned URL generation error:", error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate upload URL',
        });
      }
    }),
});










