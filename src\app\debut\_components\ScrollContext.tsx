"use client";

import { createContext, useContext, useEffect, useState } from "react";

type ScrollContextType = {
  scrollY: number;
  scrollDirection: "up" | "down" | null;
  scrollPercentage: number;
};

const ScrollContext = createContext<ScrollContextType>({
  scrollY: 0,
  scrollDirection: null,
  scrollPercentage: 0,
});

export function ScrollProvider({ children }: { children: React.ReactNode }) {
  const [scrollData, setScrollData] = useState<ScrollContextType>({
    scrollY: 0,
    scrollDirection: null,
    scrollPercentage: 0,
  });

  useEffect(() => {
    let lastScrollY = window.scrollY;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const direction = currentScrollY > lastScrollY ? "down" : "up";
      
      // Calculate scroll percentage (0-100)
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = scrollHeight > 0 
        ? Math.min(Math.max((currentScrollY / scrollHeight) * 100, 0), 100) 
        : 0;

      setScrollData({
        scrollY: currentScrollY,
        scrollDirection: currentScrollY !== lastScrollY ? direction : null,
        scrollPercentage,
      });

      lastScrollY = currentScrollY;
    };

    // Initial calculation
    handleScroll();

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <ScrollContext.Provider value={scrollData}>
      {children}
    </ScrollContext.Provider>
  );
}

export function useScroll() {
  return useContext(ScrollContext);
} 